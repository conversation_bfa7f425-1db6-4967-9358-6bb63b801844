---
layout: home
title: Home

hero:
  name: Mediatek Mali GPU Governor
  text: Advanced GPU governor for MediaTek processors
  tagline: "Intelligent GPU frequency scaling with Rust-powered performance"
  image:
    src: /logo.png
    alt: Mediatek Mali GPU Governor
  actions:
    - theme: brand
      text: Get Started
      link: /en/guide/introduction
    - theme: alt
      text: Installation
      link: /en/guide/installation
    - theme: brand
      text: View on GitHub
      link: https://github.com/Seyud/Mediatek_Mali_GPU_Governor
    - theme: alt
      text: 💬 Join Telegram
      link: https://t.me/Mediatek_Mali_GPU_Governor

features:
  - title: 🦀 Rust-powered Engine
    details: High-performance core engine developed in Rust with multithreaded monitoring for memory safety and zero-cost abstractions.
  - title: 🎮 Smart Gaming Mode
    details: Automatically detects gaming applications and applies performance-optimized GPU frequency strategies for the best gaming experience.
  - title: 🖥️ Modern WebUI
    details: Miuix-styled graphical management interface based on KernelSU API with dark/light theme support and multi-language interface.
  - title: ⚙️ Highly Customizable
    details: Flexible configuration system supporting custom GPU frequency tables, voltage settings, and four different performance modes.
