name: ci-build
'on':
  push:
    paths:
      # Rust源代码
      - 'src/**'
      # Cargo配置文件
      - 'Cargo.toml'
      - 'Cargo.lock'
      # Git子模块配置
      - '.gitmodules'
      - 'module'
      # 构建脚本
      - 'build.py'
      - 'build_config.toml'
      # 工作流文件本身
      - '.github/workflows/build.yml'
env:
  CARGO_TERM_COLOR: always
jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: recursive
      - name: Setup ndk
        run: |
          ndk_url=$(wget -qO- https://github.com/android/ndk/releases/latest | grep -e 'https://dl.google.com/android/repository/android-ndk-.*-linux.zip' | sed -n 's/.*<a href="\([^"]*\)".*/\1/p')
          wget -O ndk.zip $ndk_url -nv
          mkdir -p ~/ndk_temp
          unzip ndk.zip -d ~/ndk_temp 2>&1 > /dev/null
          mv ~/ndk_temp/*/* ~/ndk_temp
      - name: Cache Rust dependencies
        uses: actions/cache@v4
        id: cache-cargo
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            ${{ env.CARGO_TARGET_DIR }}
          key: cargo-${{ runner.os }}-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            cargo-${{ runner.os }}-
      - name: Setup Rust toolchains
        run: |
          rustup default nightly
          rustup target add aarch64-linux-android
          rustup component add rust-src
      - name: Install build dependencies
        run: |
          sudo apt update
          sudo apt install -y gcc-multilib
          cargo install cargo-ndk --locked
      - name: Build
        run: |
          export ANDROID_NDK_HOME=~/ndk_temp
          export ANDROID_NDK_ROOT=~/ndk_temp
          # 构建发布版本
          cargo ndk -t arm64-v8a build -r
          # 准备模块包
          cp target/aarch64-linux-android/release/gpugovernor module/bin/
          rm -rf module/.git
          # 创建临时目录用于打包
          mkdir -p temp_package
          # 复制指定的文件夹
          cp -r module/bin temp_package/
          cp -r module/config temp_package/
          cp -r module/docs temp_package/
          cp -r module/META-INF temp_package/
          cp -r module/script temp_package/
          cp -r module/webroot temp_package/
          # 复制指定的文件
          cp module/action.sh temp_package/
          cp module/customize.sh temp_package/

          cp module/module.prop temp_package/
          cp module/service.sh temp_package/
          cp module/uninstall.sh temp_package/
          cp module/volt_list.txt temp_package/
          # 创建 zip 文件
          cd temp_package/
          zip -r ../Mediatek_Mali_GPU_Governor.zip .
      - name: Prepare Upload Info
        id: prep
        run: |
          echo "CARGO_VERSION=$(grep '^version' Cargo.toml | sed 's/version = "\(.*\)"/\1/')" >> $GITHUB_ENV
          echo "SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)" >> $GITHUB_ENV
      - name: Upload
        env:
          BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
          CARGO_VERSION: ${{ env.CARGO_VERSION }}
          COMMIT_MESSAGE: |+
            🚀 **Mediatek Mali GPU Governor Build \#${{ github.run_number }}**
            📋 **Build Info:**
            Version: `v${{ env.CARGO_VERSION }}`
            [Commit](${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }})
            📝 **Commit Message:**
            ```
            ${{ format('{0}', github.event.head_commit.message) }}
            ```
            by `${{ github.event.head_commit.author.name }}`
            
            🔗 [View Workflow](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
        run: |
          sudo apt-get update && sudo apt-get install -y jq
          ESCAPED_MESSAGE=$(echo "${COMMIT_MESSAGE}" | jq -R -s '.')
          
          curl -v "https://api.telegram.org/bot${BOT_TOKEN}/sendMediaGroup" \
               -F "chat_id=${CHAT_ID}" \
               -F "message_thread_id=15" \
               -F "media=[{\"type\":\"document\",\"media\":\"attach://file\",\"parse_mode\":\"MarkdownV2\",\"caption\":${ESCAPED_MESSAGE}}]" \
               -F "file=@Mediatek_Mali_GPU_Governor.zip"
