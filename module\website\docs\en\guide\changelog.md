---
layout: doc
---

# 📝 CHANGELOG

## 🚀 v2.8.0 → v2.9.0 (August 3, 2025)

> ⚠️ **Important Notice**
>
> Due to significant configuration file changes, it is recommended to backup old configuration files, uninstall the module, reboot, and then install.

### ✨ New Features

- **Add custom configuration feature** ⚙️
  - Users can now customize module behavior, providing more personalized options. Details of custom configuration can be found in the module docs folder.
- **Add numerous adjustable configuration items** 🛠️
  - Added a large number of configurable parameters, allowing users to control module functions more precisely.
- **Separate Margin configuration item from frequency table to custom configuration** 📊
  - Margin configuration is now independent of the frequency table, and users can adjust it separately to improve configuration flexibility.

### 🔧 Improvements

- **Optimize welcome message** 👋
  - Improved the display effect of the welcome message during module installation and startup.
- **Optimize log rotation at startup** 📒
  - Improved the efficiency and stability of log rotation at startup.
- **Refactor log rotation function** 🔄
  - Refactored the log rotation mechanism to improve code quality and maintainability.
- **Refactor game detection** 🎮
  - Redesigned game detection logic to improve accuracy.
- **Remove game mode file, add current mode file** 📄
  - Simplified mode management by removing the game mode file and adding a current mode file for unified management.
- **Optimize module scripts** 🧠
  - Optimized module scripts to improve execution efficiency and stability.
- **Refactor game list** 🕹️
  - Redesigned the game list management mechanism to improve maintainability and scalability.
- **Refactor frequency table** 📈
  - Refactored the frequency table structure to optimize data management and access efficiency.
- **Refactor WebUI modular architecture** 🌐
  - The WebUI part has been refactored into a modular architecture to improve code structure and maintainability.

### 🐛 Bug Fixes

- **Fixed some known issues** 🛠️
  - Resolved potential abnormal situations in specific scenarios.

### 🗑️ Removals/Adjustments

- **Adjust configuration file structure** 📁
  - Reorganized the configuration file structure to make it clearer and easier to manage.