[5.3.3 / 2023-11-30](https://github.com/clean-css/clean-css/compare/v5.3.2...v5.3.3)
==================

* Fixed issue [#1262](https://github.com/clean-css/clean-css/issues/1262) - dynamically require os for edge runtime compatibility.

[5.3.2 / 2023-01-19](https://github.com/clean-css/clean-css/compare/v5.3.1...v5.3.2)
==================

* Fixed issue [#1224](https://github.com/clean-css/clean-css/issues/1224) - incorrect parsing of selectors with double hyphen.
* Fixed issue [#1228](https://github.com/clean-css/clean-css/issues/1228) - incorrect appending of '%' inside rgba colors.
* Fixed issue [#1232](https://github.com/clean-css/clean-css/issues/1232) - support for `@container` keyword.
* Fixed issue [#1239](https://github.com/clean-css/clean-css/issues/1239) - edge case in handling `@import` statements.
* Fixed issue [#1242](https://github.com/clean-css/clean-css/issues/1242) - support for `@layer` keyword.

[5.3.1 / 2022-07-13](https://github.com/clean-css/clean-css/compare/v5.3.0...v5.3.1)
==================

* Fixed issue [#1218](https://github.com/clean-css/clean-css/issues/1218) - double hyphen in at-rule breaks parsing.
* Fixed issue [#1220](https://github.com/clean-css/clean-css/issues/1220) - adds optimization for nth-* rules.

[5.3.0 / 2022-03-31](https://github.com/clean-css/clean-css/compare/v5.2.3...v5.3.0)
==================

* Adds customizable value optimizers for variables.
* Fixed issue [#1159](https://github.com/clean-css/clean-css/issues/1159) - adds optimization for `nth-child` and `nth-of-type`.
* Fixed issue [#1181](https://github.com/clean-css/clean-css/issues/1181) - CSS level 4 color functions with spaces.
* Fixed issue [#1183](https://github.com/clean-css/clean-css/issues/1183) - fraction optimizer breaks `image-set`.
* Fixed issue [#1208](https://github.com/clean-css/clean-css/issues/1208) - handling generic family names.
* Fixed issue [#1210](https://github.com/clean-css/clean-css/issues/1210) - handling `file://` protocol.

[5.2.4 / 2022-01-28](https://github.com/clean-css/clean-css/compare/v5.2.3...v5.2.4)
==================

* Fixed issue [#1196](https://github.com/clean-css/clean-css/issues/1196) - correctly parse variables & comments mix.

[5.2.3 / 2022-01-26](https://github.com/clean-css/clean-css/compare/v5.2.2...v5.2.3)
==================

* Fixed issue [#1185](https://github.com/clean-css/clean-css/issues/1185) - keeping comments inside variables.
* Fixed issue [#1194](https://github.com/clean-css/clean-css/issues/1194) - unexpected end of JSON input when source map is empty.

[5.2.2 / 2021-10-21](https://github.com/clean-css/clean-css/compare/v5.2.1...v5.2.2)
==================

* Fixed an unsafe data URI regex, which, when clean-css is used as a service, could be used in a DOS attack.

[5.2.1 / 2021-09-30](https://github.com/clean-css/clean-css/compare/v5.2.0...v5.2.1)
==================

* Fixed issue [#1186](https://github.com/clean-css/clean-css/issues/1186) - bad error handling in batch mode with promises.

[5.2.0 / 2021-09-25](https://github.com/clean-css/clean-css/compare/5.1...v5.2.0)
==================

* Fixed issue [#1180](https://github.com/clean-css/clean-css/issues/1180) - properly handle empty variable values.

[5.1.5 / 2021-08-05](https://github.com/clean-css/clean-css/compare/v5.1.4...v5.1.5)
==================

* Fixed issue [#1178](https://github.com/clean-css/clean-css/issues/1178) - fixes lack of space removal in variable blocks.

[5.1.4 / 2021-07-29](https://github.com/clean-css/clean-css/compare/v5.1.3...v5.1.4)
==================

* Fixed issue [#1177](https://github.com/clean-css/clean-css/issues/1177) - fix to missing local imports when only remote ones allowed.

[5.1.3 / 2021-06-25](https://github.com/clean-css/clean-css/compare/v5.1.2...v5.1.3)
==================

* Fixed issue [#1160](https://github.com/clean-css/clean-css/issues/1160) - keep zero units when inside multiple functions.
* Fixed issue [#1161](https://github.com/clean-css/clean-css/issues/1161) - extra whitespace in URLs.
* Fixed issue [#1166](https://github.com/clean-css/clean-css/issues/1166) - incorrect compoment splitting when empty multiplex part.

[5.1.2 / 2021-03-19](https://github.com/clean-css/clean-css/compare/v5.1.1...v5.1.2)
==================

* Fixed issue [#996](https://github.com/clean-css/clean-css/issues/996) - space removed from pseudo classes.

[5.1.1 / 2021-03-03](https://github.com/clean-css/clean-css/compare/v5.1.0...v5.1.1)
==================

* Fixed issue [#1156](https://github.com/clean-css/clean-css/issues/1156) - invalid hsl/hsla validation in level 2 optimizations.

[5.1.0 / 2021-02-18](https://github.com/clean-css/clean-css/compare/5.0...v5.1.0)
==================

* Fixes stripping '%' from inside color functions.
* Improves tokenization speed by ~30%.
* Fixed issue [#1143](https://github.com/clean-css/clean-css/issues/1143) - some missing level 1 value optimizations.


[5.0.1 / 2021-01-29](https://github.com/clean-css/clean-css/compare/v5.0.0...v5.0.1)
==================

* Fixed issue [#1139](https://github.com/clean-css/clean-css/issues/1139) - overriding & merging properties without `canOverride` set.

[5.0.0 / 2021-01-29](https://github.com/clean-css/clean-css/compare/4.2...v5.0.0)
==================

* Adds a way process input files without bundling it into one big output file.
* Adds level 1 and level 2 optimization plugins.
* Disables URL rebasing by default.
* Disables URL unquoting by default.
* Drops support for Node.js 6 & 8 to support last 3 Node.js releases: 10, 12, and 14.
* Fixed issue [#889](https://github.com/clean-css/clean-css/issues/889) - whitelisted level 1 optimizations.
* Fixed issue [#975](https://github.com/clean-css/clean-css/issues/975) - incorrect block name optimization.
* Fixed issue [#1009](https://github.com/clean-css/clean-css/issues/1009) - whitespace around comments.
* Fixed issue [#1021](https://github.com/clean-css/clean-css/issues/1021) - allow one- and two-letter property names.
* Fixed issue [#1022](https://github.com/clean-css/clean-css/issues/1022) - merging into shorthands new property positioning.
* Fixed issue [#1032](https://github.com/clean-css/clean-css/issues/1032) - wrong order of merged shorthands with inherit.
* Fixed issue [#1043](https://github.com/clean-css/clean-css/issues/1043) - `calc` fallback removed within other function.
* Fixed issue [#1045](https://github.com/clean-css/clean-css/issues/1045) - non-standard protocol-less URL first slash removed.
* Fixed issue [#1050](https://github.com/clean-css/clean-css/issues/1050) - correctly keeps default animation duration if delay is also set.
* Fixed issue [#1053](https://github.com/clean-css/clean-css/issues/1053) - treats `calc()` as first class width value.
* Fixed issue [#1055](https://github.com/clean-css/clean-css/issues/1055) - supports 4- and 8-character hex with alpha color notation.
* Fixed issue [#1057](https://github.com/clean-css/clean-css/issues/1057) - level 2 optimizations and quoted font family name.
* Fixed issue [#1059](https://github.com/clean-css/clean-css/issues/1059) - animation time units as CSS expressions.
* Fixed issue [#1060](https://github.com/clean-css/clean-css/issues/1060) - variable removed when shorthand's only value.
* Fixed issue [#1062](https://github.com/clean-css/clean-css/issues/1062) - wrong optimization of CSS pseudo-classes with selector list.
* Fixed issue [#1073](https://github.com/clean-css/clean-css/issues/1073) - adds support for non-standard `rpx` units.
* Fixed issue [#1075](https://github.com/clean-css/clean-css/issues/1075) - media merging and variables.
* Fixed issue [#1087](https://github.com/clean-css/clean-css/issues/1087) - allow units with any case.
* Fixed issue [#1088](https://github.com/clean-css/clean-css/issues/1088) - building source maps with source preserved via comments.
* Fixed issue [#1090](https://github.com/clean-css/clean-css/issues/1090) - edge case in merging for `border` and `border-image`.
* Fixed issue [#1103](https://github.com/clean-css/clean-css/issues/1103) - don't allow merging longhand into `unset` shorthand.
* Fixed issue [#1115](https://github.com/clean-css/clean-css/issues/1115) - incorrect multiplex longhand into shorthand merging.
* Fixed issue [#1117](https://github.com/clean-css/clean-css/issues/1117) - don't change zero values inside `min`, `max`, and `clamp` functions.
* Fixed issue [#1122](https://github.com/clean-css/clean-css/issues/1122) - don't wrap data URI in single quotes.
* Fixed issue [#1125](https://github.com/clean-css/clean-css/issues/1125) - quotes stripped from withing `@supports` clause.
* Fixed issue [#1128](https://github.com/clean-css/clean-css/issues/1128) - take variables into account when reordering properties.
* Fixed issue [#1132](https://github.com/clean-css/clean-css/issues/1132) - vendor-prefixed classes inside `:not()`.
* Reworks all level 1 optimizations to conform to plugin style.

[4.2.3 / 2020-01-28](https://github.com/clean-css/clean-css/compare/v4.2.2...v4.2.3)
==================

* Fixed issue [#1106](https://github.com/clean-css/clean-css/issues/1106) - regression in handling RGBA/HSLA colors.

[4.2.2 / 2020-01-25](https://github.com/clean-css/clean-css/compare/v4.2.1...v4.2.2)
==================

* Fixed error when property block has no value.
* Fixed issue [#1077](https://github.com/clean-css/clean-css/issues/1077) - local fonts with color in name.
* Fixed issue [#1082](https://github.com/clean-css/clean-css/issues/1082) - correctly convert colors if alpha >= 1.
* Fixed issue [#1085](https://github.com/clean-css/clean-css/issues/1085) - prevent unquoting of grid elements.

[4.2.1 / 2018-08-07](https://github.com/clean-css/clean-css/compare/v4.2.0...v4.2.1)
==================

* Fixes giving `breakWith` option via a string.

[4.2.0 / 2018-08-02](https://github.com/clean-css/clean-css/compare/4.1...v4.2.0)
==================

* Adds `process` method for compatibility with optimize-css-assets-webpack-plugin.
* Fixed issue [#861](https://github.com/clean-css/clean-css/issues/861) - new `transition` property optimizer.
* Fixed issue [#895](https://github.com/clean-css/clean-css/issues/895) - ignoring specific styles.
* Fixed issue [#947](https://github.com/clean-css/clean-css/issues/947) - selector based filtering.
* Fixed issue [#964](https://github.com/clean-css/clean-css/issues/964) - adds configurable line breaks.
* Fixed issue [#986](https://github.com/clean-css/clean-css/issues/986) - level 2 optimizations and CSS 4 colors.
* Fixed issue [#1000](https://github.com/clean-css/clean-css/issues/1000) - carriage return handling in tokenizer.
* Fixed issue [#1038](https://github.com/clean-css/clean-css/issues/1038) - `font-variation-settings` quoting.
* Fixes ReDOS vulnerabilities in validator code.

[4.1.11 / 2018-03-06](https://github.com/clean-css/clean-css/compare/v4.1.10...v4.1.11)
==================

* Backports fixes to ReDOS vulnerabilities in validator code.

[4.1.10 / 2018-03-05](https://github.com/clean-css/clean-css/compare/v4.1.9...v4.1.10)
==================

* Fixed issue [#988](https://github.com/clean-css/clean-css/issues/988) - edge case in dropping default animation-duration.
* Fixed issue [#989](https://github.com/clean-css/clean-css/issues/989) - edge case in removing unused at rules.
* Fixed issue [#1001](https://github.com/clean-css/clean-css/issues/1001) - corrupted tokenizer state.
* Fixed issue [#1006](https://github.com/clean-css/clean-css/issues/1006) - edge case in handling invalid source maps.
* Fixed issue [#1008](https://github.com/clean-css/clean-css/issues/1008) - edge case in breaking up `font` shorthand.

[4.1.9 / 2017-09-19](https://github.com/clean-css/clean-css/compare/v4.1.8...v4.1.9)
==================

* Fixed issue [#971](https://github.com/clean-css/clean-css/issues/971) - edge case in removing unused at rules.

[4.1.8 / 2017-09-02](https://github.com/clean-css/clean-css/compare/v4.1.7...v4.1.8)
==================

* Fixed issue [#959](https://github.com/clean-css/clean-css/issues/959) - regression in shortening long hex values.
* Fixed issue [#960](https://github.com/clean-css/clean-css/issues/960) - better explanation of `efficiency` stat.
* Fixed issue [#965](https://github.com/clean-css/clean-css/issues/965) - edge case in parsing comment endings.
* Fixed issue [#966](https://github.com/clean-css/clean-css/issues/966) - remote `@import`s referenced from local ones.

[4.1.7 / 2017-07-14](https://github.com/clean-css/clean-css/compare/v4.1.6...v4.1.7)
==================

* Fixed issue [#957](https://github.com/clean-css/clean-css/issues/957) - `0%` minification of `width` property.

[4.1.6 / 2017-07-08](https://github.com/clean-css/clean-css/compare/v4.1.5...v4.1.6)
==================

* Fixed issue [#887](https://github.com/clean-css/clean-css/issues/887) - edge case in serializing comments.
* Fixed issue [#953](https://github.com/clean-css/clean-css/issues/953) - beautify breaks attribute selectors.

[4.1.5 / 2017-06-29](https://github.com/clean-css/clean-css/compare/v4.1.4...v4.1.5)
==================

* Fixed issue [#945](https://github.com/clean-css/clean-css/issues/945) - hex RGBA colors in IE filters.
* Fixed issue [#952](https://github.com/clean-css/clean-css/issues/952) - parsing `@page` according to CSS3 spec.

[4.1.4 / 2017-06-14](https://github.com/clean-css/clean-css/compare/v4.1.3...v4.1.4)
==================

* Fixed issue [#950](https://github.com/clean-css/clean-css/issues/950) - bug in removing unused `@font-face` rules.

[4.1.3 / 2017-05-18](https://github.com/clean-css/clean-css/compare/v4.1.2...v4.1.3)
==================

* Fixed issue [#946](https://github.com/clean-css/clean-css/issues/946) - tokenizing `-ms-grid-columns` repeat syntax.

[4.1.2 / 2017-05-10](https://github.com/clean-css/clean-css/compare/v4.1.1...v4.1.2)
==================

* Fixed issue [#939](https://github.com/clean-css/clean-css/issues/939) - semicolon after `@apply` at rule.
* Fixed issue [#940](https://github.com/clean-css/clean-css/issues/940) - handling more `font` keywords.
* Fixed issue [#941](https://github.com/clean-css/clean-css/issues/941) - breaking up vendor prefixed `animation`.

[4.1.1 / 2017-05-08](https://github.com/clean-css/clean-css/compare/v4.1.0...v4.1.1)
==================

* Fixed issue [#938](https://github.com/clean-css/clean-css/issues/938) - removing unused at-rules with `!important`.

[4.1.0 / 2017-05-07](https://github.com/clean-css/clean-css/compare/4.0...v4.1.0)
==================

* Improves longhand-into-shorthand merging mechanism in complex cases as with `border-*` shorthands.
* Fixed issue [#254](https://github.com/clean-css/clean-css/issues/254) - adds `font` property optimizer.
* Fixed issue [#525](https://github.com/clean-css/clean-css/issues/525) - restores `inherit`-based merging.
* Fixed issue [#755](https://github.com/clean-css/clean-css/issues/755) - adds custom handling of remote requests.
* Fixed issue [#860](https://github.com/clean-css/clean-css/issues/860) - adds `animation` property optimizer.
* Fixed issue [#862](https://github.com/clean-css/clean-css/issues/862) - allows removing unused at rules.
* Fixed issue [#886](https://github.com/clean-css/clean-css/issues/886) - better multi pseudo class / element merging.
* Fixed issue [#890](https://github.com/clean-css/clean-css/issues/890) - adds toggle to disable empty tokens removal.
* Fixed issue [#893](https://github.com/clean-css/clean-css/issues/893) - `inline: false` as alias to `inline: 'none'`.
* Fixed issue [#905](https://github.com/clean-css/clean-css/issues/905) - allows disabling selector sorting.
* Fixed issue [#906](https://github.com/clean-css/clean-css/issues/906) - improves usability of web UI settings.
* Fixed issue [#908](https://github.com/clean-css/clean-css/issues/908) - improved `minify` method signature.
* Fixed issue [#916](https://github.com/clean-css/clean-css/issues/916) - maximum number of merged selectors.
* Fixed issue [#920](https://github.com/clean-css/clean-css/issues/920) - allows skipping certain properties in level 2 optimizations.
* Fixed issue [#934](https://github.com/clean-css/clean-css/issues/934) - smarter longhand into shorthand merging.

[4.0.13 / 2017-05-10](https://github.com/clean-css/clean-css/compare/v4.0.12...v4.0.13)
==================

* Backports [#939](https://github.com/clean-css/clean-css/issues/939) - semicolon after `@apply` at rule.

[4.0.12 / 2017-04-12](https://github.com/clean-css/clean-css/compare/v4.0.11...v4.0.12)
==================

* Fixed issue [#930](https://github.com/clean-css/clean-css/issues/930) - regression in tidying selectors.

[4.0.11 / 2017-04-04](https://github.com/clean-css/clean-css/compare/v4.0.10...v4.0.11)
==================

* Fixed issue [#924](https://github.com/clean-css/clean-css/issues/924) - `hsl` zero percent eager optimization.

[4.0.10 / 2017-03-22](https://github.com/clean-css/clean-css/compare/v4.0.9...v4.0.10)
==================

* Fixed issue [#917](https://github.com/clean-css/clean-css/issues/917) - prevents grid area unquoting.
* Backported [#916](https://github.com/clean-css/clean-css/issues/916) - maximum number of merged selectors.
* Refixed issue [#556](https://github.com/clean-css/clean-css/issues/556) - IE backslash hacks.

[4.0.9 / 2017-03-15](https://github.com/clean-css/clean-css/compare/v4.0.8...v4.0.9)
==================

* Fixed issue [#902](https://github.com/clean-css/clean-css/issues/902) - case insensitive attribute matchers.
* Fixed issue [#903](https://github.com/clean-css/clean-css/issues/903) - web UI and source maps.
* Fixed issue [#907](https://github.com/clean-css/clean-css/issues/907) - space after closing brace in `@supports`.
* Fixed issue [#910](https://github.com/clean-css/clean-css/issues/910) - too aggressive precision optimizations.

[4.0.8 / 2017-02-22](https://github.com/clean-css/clean-css/compare/v4.0.7...v4.0.8)
==================

* Fixes edge case in remote stylesheet fetching.
* Fixed issue [#899](https://github.com/clean-css/clean-css/issues/899) - regression in optimizing pseudo class arguments.

[4.0.7 / 2017-02-14](https://github.com/clean-css/clean-css/compare/v4.0.6...v4.0.7)
==================

* Fixed issue [#891](https://github.com/clean-css/clean-css/issues/891) - merging vendor-prefixed pseudo-classes.

[4.0.6 / 2017-02-10](https://github.com/clean-css/clean-css/compare/v4.0.5...v4.0.6)
==================

* Fixed issue [#885](https://github.com/clean-css/clean-css/issues/885) - unquoting `font-feature-settings`.

[4.0.5 / 2017-02-07](https://github.com/clean-css/clean-css/compare/v4.0.4...v4.0.5)
==================

* Fixed issue [#884](https://github.com/clean-css/clean-css/issues/884) - handling absolute paths on Windows.
* Fixed issue [#881](https://github.com/clean-css/clean-css/issues/881) - incorrect `require` arity.
* Fixed issue [#880](https://github.com/clean-css/clean-css/issues/880) - incorrect token type identification.

[4.0.4 / 2017-02-04](https://github.com/clean-css/clean-css/compare/v4.0.3...v4.0.4)
==================

* Fixed issue [#879](https://github.com/clean-css/clean-css/issues/879) - incorrect handling of spaces in paths.
* Fixed issue [#878](https://github.com/clean-css/clean-css/issues/878) - invalid double backslash tokenization.

[4.0.3 / 2017-01-30](https://github.com/clean-css/clean-css/compare/v4.0.2...v4.0.3)
==================

* Fixed issue [#875](https://github.com/clean-css/clean-css/issues/875) - invalid traversing in semantic merging.

[4.0.2 / 2017-01-26](https://github.com/clean-css/clean-css/compare/v4.0.1...v4.0.2)
==================

* Fixed issue [#874](https://github.com/clean-css/clean-css/issues/874) - regression in at-rule tokenization.

[4.0.1 / 2017-01-25](https://github.com/clean-css/clean-css/compare/v4.0.0...v4.0.1)
==================

* Fixed issue [#866](https://github.com/clean-css/clean-css/issues/866) - edge case in `inline` option.
* Fixed issue [#867](https://github.com/clean-css/clean-css/issues/867) - skip optimizing variable values.
* Fixed issue [#868](https://github.com/clean-css/clean-css/issues/868) - accept absolute paths in input hash.
* Fixed issue [#872](https://github.com/clean-css/clean-css/issues/872) - edge case in CSS tokenization.

[4.0.0 / 2017-01-23](https://github.com/clean-css/clean-css/compare/v3.4.24...v4.0.0)
==================

* Adds more detailed error & warning messages on top of the new tokenizer.
* Disables restructuring optimizations by default until optimized in #533.
* Fixes a bug ignoring incorrect properties in complex restructuring.
* Requires Node.js 4.0+ to run.
* Removes `benchmark` API option as total time is always reported under `stats` property.
* Removes `debug` API switch as stats are always gathered and available under `stats` property.
* Replaces the old tokenizer with a new one which doesn't use any escaping.
* Replaces the old `@import` inlining with one on top of the new tokenizer.
* Re-enables `background-(clip|origin|size)` merging with `background` shorthand.
* Simplifies URL rebasing with a single `rebaseTo` option in API or inferred from `--output` in CLI.
* Splits `inliner` option into `inlineRequest` and `inlineTimeout`.
* Fixed issue [#209](https://github.com/clean-css/clean-css/issues/209) - adds output formatting via `format` flag.
* Fixed issue [#290](https://github.com/clean-css/clean-css/issues/290) - removes aggressive merging.
* Fixed issue [#432](https://github.com/clean-css/clean-css/issues/432) - adds URLs normalization.
* Fixed issue [#460](https://github.com/clean-css/clean-css/issues/460) - unescaped semicolon in selector.
* Fixed issue [#657](https://github.com/clean-css/clean-css/issues/657) - adds property name validation.
* Fixed issue [#685](https://github.com/clean-css/clean-css/issues/685) - adds lowercasing hex colors optimization.
* Fixed issue [#686](https://github.com/clean-css/clean-css/issues/686) - adds rounding precision for all units.
* Fixed issue [#703](https://github.com/clean-css/clean-css/issues/703) - changes default IE compatibility to 10+.
* Fixed issue [#731](https://github.com/clean-css/clean-css/issues/731) - adds granular control over level 2 optimizations.
* Fixed issue [#739](https://github.com/clean-css/clean-css/issues/739) - error when a closing brace is missing.
* Fixed issue [#750](https://github.com/clean-css/clean-css/issues/750) - allows `width` overriding.
* Fixed issue [#756](https://github.com/clean-css/clean-css/issues/756) - adds disabling font-weight optimizations.
* Fixed issue [#758](https://github.com/clean-css/clean-css/issues/758) - ignores rules with empty selector.
* Fixed issue [#767](https://github.com/clean-css/clean-css/issues/767) - disables remote `@import` inlining by default.
* Fixed issue [#773](https://github.com/clean-css/clean-css/issues/773) - adds reordering based on selector specificity.
* Fixed issue [#785](https://github.com/clean-css/clean-css/issues/785) - adds `@font-face` de-duplication.
* Fixed issue [#791](https://github.com/clean-css/clean-css/issues/791) - resolves imports in-memory if possible.
* Fixed issue [#796](https://github.com/clean-css/clean-css/issues/796) - semantic merging for `@media` blocks.
* Fixed issue [#801](https://github.com/clean-css/clean-css/issues/801) - smarter `@import` inlining.
* Fixed issue [#806](https://github.com/clean-css/clean-css/issues/806) - skip optimizing variable properties.
* Fixed issue [#817](https://github.com/clean-css/clean-css/issues/817) - makes `off` disable rounding.
* Fixed issue [#818](https://github.com/clean-css/clean-css/issues/818) - disables `px` rounding by default.
* Fixed issue [#828](https://github.com/clean-css/clean-css/issues/828) - `-chrome-` hack support.
* Fixed issue [#829](https://github.com/clean-css/clean-css/issues/829) - adds more strict selector merging rules.
* Fixed issue [#834](https://github.com/clean-css/clean-css/issues/834) - adds extra line break in nested blocks.
* Fixed issue [#836](https://github.com/clean-css/clean-css/issues/836) - enables level `0` optimizations.
* Fixed issue [#839](https://github.com/clean-css/clean-css/issues/839) - allows URIs in import inlining rules.
* Fixed issue [#840](https://github.com/clean-css/clean-css/issues/840) - allows input source map as map object.
* Fixed issue [#843](https://github.com/clean-css/clean-css/issues/843) - regression in selector handling.
* Fixed issue [#845](https://github.com/clean-css/clean-css/issues/845) - web compatibility of 4.0 branch.
* Fixed issue [#847](https://github.com/clean-css/clean-css/issues/847) - regression in handling invalid selectors.
* Fixed issue [#849](https://github.com/clean-css/clean-css/issues/849) - disables inlining protocol-less resources.
* Fixed issue [#856](https://github.com/clean-css/clean-css/issues/856) - allows `minify` to return a promise.
* Fixed issue [#857](https://github.com/clean-css/clean-css/issues/857) - normalizes CleanCSS API interface.
* Fixed issue [#863](https://github.com/clean-css/clean-css/issues/863) - adds `transform` callback for custom optimizations.

[3.4.26 / 2017-05-10](https://github.com/clean-css/clean-css/compare/v3.4.25...v3.4.26)
==================

* Backports [#939](https://github.com/clean-css/clean-css/issues/939) - semicolon after `@apply` at-rule.

[3.4.25 / 2017-02-22](https://github.com/clean-css/clean-css/compare/v3.4.24...v3.4.25)
==================

* Fixed issue [#897](https://github.com/clean-css/clean-css/issues/897) - tokenization with escaped markers.

[3.4.24 / 2017-01-20](https://github.com/clean-css/clean-css/compare/v3.4.23...v3.4.24)
==================

* Fixed issue [#859](https://github.com/clean-css/clean-css/issues/859) - avoid `-webkit-border-radius` optimizations.

[3.4.23 / 2016-12-17](https://github.com/clean-css/clean-css/compare/v3.4.22...v3.4.23)
==================

* Fixed issue [#844](https://github.com/clean-css/clean-css/issues/844) - regression in property values extraction.

[3.4.22 / 2016-12-12](https://github.com/clean-css/clean-css/compare/v3.4.21...v3.4.22)
==================

* Fixed issue [#841](https://github.com/clean-css/clean-css/issues/841) - disabled importing and files passed as array.
* Ignores `@import` at-rules if appearing after any non-`@import` rules.

[3.4.21 / 2016-11-16](https://github.com/clean-css/clean-css/compare/v3.4.20...v3.4.21)
==================

* Fixed issue [#821](https://github.com/clean-css/clean-css/issues/821) - reducing non-adjacent rules.
* Fixed issue [#830](https://github.com/clean-css/clean-css/issues/830) - reordering border-* properties.
* Fixed issue [#833](https://github.com/clean-css/clean-css/issues/833) - moving `@media` queries.

[3.4.20 / 2016-09-26](https://github.com/clean-css/clean-css/compare/v3.4.19...v3.4.20)
==================

* Fixed issue [#814](https://github.com/clean-css/clean-css/issues/814) - `:selection` rule merging.

[3.4.19 / 2016-07-25](https://github.com/clean-css/clean-css/compare/v3.4.18...v3.4.19)
==================

* Fixed issue [#795](https://github.com/clean-css/clean-css/issues/795) - `!important` and override compacting.

[3.4.18 / 2016-06-15](https://github.com/clean-css/clean-css/compare/v3.4.17...v3.4.18)
==================

* Fixed issue [#787](https://github.com/clean-css/clean-css/issues/787) - regression in processing data URIs.

[3.4.17 / 2016-06-04](https://github.com/clean-css/clean-css/compare/v3.4.16...v3.4.17)
==================

* Fixed issue [#783](https://github.com/clean-css/clean-css/issues/783) - regression in processing data URIs.

[3.4.16 / 2016-06-02](https://github.com/clean-css/clean-css/compare/v3.4.15...v3.4.16)
==================

* Fixed issue [#781](https://github.com/clean-css/clean-css/issues/781) - regression in override compacting.
* Fixed issue [#782](https://github.com/clean-css/clean-css/issues/782) - regression in processing data URIs.

[3.4.15 / 2016-06-01](https://github.com/clean-css/clean-css/compare/v3.4.14...v3.4.15)
==================

* Fixed issue [#776](https://github.com/clean-css/clean-css/issues/776) - edge case in quoted data URIs.
* Fixed issue [#779](https://github.com/clean-css/clean-css/issues/779) - merging `background-(position|size)`.
* Fixed issue [#780](https://github.com/clean-css/clean-css/issues/780) - space after inlined variables.

[3.4.14 / 2016-05-31](https://github.com/clean-css/clean-css/compare/v3.4.13...v3.4.14)
==================

* Fixed issue [#751](https://github.com/clean-css/clean-css/issues/751) - stringifying CSS variables.
* Fixed issue [#763](https://github.com/clean-css/clean-css/issues/763) - data URI SVG and quoting.
* Fixed issue [#765](https://github.com/clean-css/clean-css/issues/765) - two values of border-radius.
* Fixed issue [#768](https://github.com/clean-css/clean-css/issues/768) - invalid border-radius property.

[3.4.13 / 2016-05-23](https://github.com/clean-css/clean-css/compare/v3.4.12...v3.4.13)
==================

* Fixed issue [#734](https://github.com/clean-css/clean-css/issues/769) - Node.js 6.x support.

[3.4.12 / 2016-04-09](https://github.com/clean-css/clean-css/compare/v3.4.11...v3.4.12)
==================

* Fixed issue [#734](https://github.com/clean-css/clean-css/issues/734) - `--root` option edge case.
* Fixed issue [#758](https://github.com/clean-css/clean-css/issues/758) - treats empty rule as unmergeable.

[3.4.11 / 2016-04-01](https://github.com/clean-css/clean-css/compare/v3.4.10...v3.4.11)
==================

* Fixed issue [#738](https://github.com/clean-css/clean-css/issues/738) - edge case in comment processing.
* Fixed issue [#741](https://github.com/clean-css/clean-css/issues/741) - HTTP proxy with HTTPS inlining.
* Fixed issue [#743](https://github.com/clean-css/clean-css/issues/743) - background shorthand and source maps.
* Fixed issue [#745](https://github.com/clean-css/clean-css/issues/745) - matching mixed case `!important`.

[3.4.10 / 2016-02-29](https://github.com/clean-css/clean-css/compare/v3.4.9...v3.4.10)
==================

* Fixed issue [#735](https://github.com/clean-css/clean-css/issues/735) - whitespace removal with escaped chars.

[3.4.9 / 2016-01-03](https://github.com/clean-css/clean-css/compare/v3.4.8...v3.4.9)
==================

* Sped up merging by body advanced optimization.
* Fixed issue [#693](https://github.com/clean-css/clean-css/issues/693) - restructuring edge case.
* Fixed issue [#711](https://github.com/clean-css/clean-css/issues/711) - border fuzzy matching.
* Fixed issue [#714](https://github.com/clean-css/clean-css/issues/714) - stringifying property level at rules.
* Fixed issue [#715](https://github.com/clean-css/clean-css/issues/715) - stack too deep in comment scan.

[3.4.8 / 2015-11-13](https://github.com/clean-css/clean-css/compare/v3.4.7...v3.4.8)
==================

* Fixed issue [#676](https://github.com/clean-css/clean-css/issues/676) - fuzzy matching unqoted data URIs.

[3.4.7 / 2015-11-10](https://github.com/clean-css/clean-css/compare/v3.4.6...v3.4.7)
==================

* Fixed issue [#692](https://github.com/clean-css/clean-css/issues/692) - edge case in URL quoting.
* Fixed issue [#695](https://github.com/clean-css/clean-css/issues/695) - shorthand overriding edge case.
* Fixed issue [#699](https://github.com/clean-css/clean-css/issues/699) - IE9 transparent hack.
* Fixed issue [#701](https://github.com/clean-css/clean-css/issues/701) - `url` quoting with hash arguments.

[3.4.6 / 2015-10-14](https://github.com/clean-css/clean-css/compare/v3.4.5...v3.4.6)
==================

* Fixed issue [#679](https://github.com/clean-css/clean-css/issues/679) - wrong rebasing of remote URLs.

[3.4.5 / 2015-09-28](https://github.com/clean-css/clean-css/compare/v3.4.4...v3.4.5)
==================

* Fixed issue [#681](https://github.com/clean-css/clean-css/issues/681) - property inheritance & restructuring.
* Fixed issue [#675](https://github.com/clean-css/clean-css/issues/675) - overriding with `!important`.

[3.4.4 / 2015-09-21](https://github.com/clean-css/clean-css/compare/v3.4.3...v3.4.4)
==================

* Fixed issue [#626](https://github.com/clean-css/clean-css/issues/626) - edge case in import rebasing.
* Fixed issue [#674](https://github.com/clean-css/clean-css/issues/674) - adjacent merging order.

[3.4.3 / 2015-09-15](https://github.com/clean-css/clean-css/compare/v3.4.2...v3.4.3)
==================

* Fixed issue [#668](https://github.com/clean-css/clean-css/issues/668) - node v4 path.join.
* Fixed issue [#669](https://github.com/clean-css/clean-css/issues/669) - adjacent overriding with `!important`.

[3.4.2 / 2015-09-14](https://github.com/clean-css/clean-css/compare/v3.4.1...v3.4.2)
==================

* Fixed issue [#598](https://github.com/clean-css/clean-css/issues/598) - restructuring border properties.
* Fixed issue [#654](https://github.com/clean-css/clean-css/issues/654) - disables length optimizations.
* Fixed issue [#655](https://github.com/clean-css/clean-css/issues/655) - shorthands override merging.
* Fixed issue [#660](https://github.com/clean-css/clean-css/issues/660) - !important token overriding.
* Fixed issue [#662](https://github.com/clean-css/clean-css/issues/662) - !important selector reducing.
* Fixed issue [#667](https://github.com/clean-css/clean-css/issues/667) - rebasing remote URLs.

[3.4.1 / 2015-08-27](https://github.com/clean-css/clean-css/compare/v3.4.0...v3.4.1)
==================

* Fixed issue [#652](https://github.com/clean-css/clean-css/issues/652) - order of restoring and removing tokens.

[3.4.0 / 2015-08-27](https://github.com/clean-css/clean-css/compare/v3.3.10...v3.4.0)
==================

* Adds an option for a fine-grained `@import` control.
* Adds unit compatibility switches to disable length optimizations.
* Adds inferring proxy settings from HTTP_PROXY environment variable.
* Adds support for Polymer / Web Components special selectors.
* Adds support for Polymer mixins.
* Adds testing source maps in batch mode.
* Unifies wrappers for simple & advanced optimizations.
* Fixed issue [#596](https://github.com/clean-css/clean-css/issues/596) - support for !ie IE<8 hack.
* Fixed issue [#599](https://github.com/clean-css/clean-css/issues/599) - support for inlined source maps.
* Fixed issue [#607](https://github.com/clean-css/clean-css/issues/607) - adds better rule reordering.
* Fixed issue [#612](https://github.com/clean-css/clean-css/issues/612) - adds HTTP proxy support.
* Fixed issue [#618](https://github.com/clean-css/clean-css/issues/618) - adds safer function validation.
* Fixed issue [#625](https://github.com/clean-css/clean-css/issues/625) - adds length unit optimizations.
* Fixed issue [#632](https://github.com/clean-css/clean-css/issues/632) - adds disabling remote `import`s.
* Fixed issue [#635](https://github.com/clean-css/clean-css/issues/635) - adds safer `0%` optimizations.
* Fixed issue [#644](https://github.com/clean-css/clean-css/issues/644) - adds time unit optimizations.
* Fixed issue [#645](https://github.com/clean-css/clean-css/issues/645) - adds bottom to top `media` merging.
* Fixed issue [#648](https://github.com/clean-css/clean-css/issues/648) - adds property level at-rule support.

[3.3.10 / 2015-08-27](https://github.com/clean-css/clean-css/compare/v3.3.9...v3.3.10)
==================

* Adds better comments + keepBreaks handling.
* Adds better text normalizing in source maps mode.
* Fixes non-adjacent optimizations for source maps.
* Fixes removing unused items.
* Improves `outline` break up with source maps.
* Refixed issue [#629](https://github.com/clean-css/clean-css/issues/629) - source maps & background shorthands.

[3.3.9 / 2015-08-09](https://github.com/clean-css/clean-css/compare/v3.3.8...v3.3.9)
==================

* Fixed issue [#640](https://github.com/clean-css/clean-css/issues/640) - URI processing regression.

[3.3.8 / 2015-08-06](https://github.com/clean-css/clean-css/compare/v3.3.7...v3.3.8)
==================

* Fixed issue [#629](https://github.com/clean-css/clean-css/issues/629) - source maps & background shorthands.
* Fixed issue [#630](https://github.com/clean-css/clean-css/issues/630) - vendor prefixed flex optimizations.
* Fixed issue [#633](https://github.com/clean-css/clean-css/issues/633) - handling data URI with brackets.
* Fixed issue [#634](https://github.com/clean-css/clean-css/issues/634) - merging :placeholder selectors.

[3.3.7 / 2015-07-29](https://github.com/clean-css/clean-css/compare/v3.3.6...v3.3.7)
==================

* Fixed issue [#616](https://github.com/clean-css/clean-css/issues/616) - ordering in restructuring.

[3.3.6 / 2015-07-14](https://github.com/clean-css/clean-css/compare/v3.3.5...v3.3.6)
==================

* Fixed issue [#620](https://github.com/clean-css/clean-css/issues/620) - `bold` style in font shorthands.

[3.3.5 / 2015-07-01](https://github.com/clean-css/clean-css/compare/v3.3.4...v3.3.5)
==================

* Fixed issue [#608](https://github.com/clean-css/clean-css/issues/608) - custom URI protocols handling.

[3.3.4 / 2015-06-24](https://github.com/clean-css/clean-css/compare/v3.3.3...v3.3.4)
==================

* Fixed issue [#610](https://github.com/clean-css/clean-css/issues/610) - `border:inherit` restoring.
* Fixed issue [#611](https://github.com/clean-css/clean-css/issues/611) - edge case in quote stripping.

[3.3.3 / 2015-06-16](https://github.com/clean-css/clean-css/compare/v3.3.2...v3.3.3)
==================

* Fixed issue [#603](https://github.com/clean-css/clean-css/issues/603) - IE suffix hack defaults to on.

[3.3.2 / 2015-06-14](https://github.com/clean-css/clean-css/compare/v3.3.1...v3.3.2)
==================

* Fixed issue [#595](https://github.com/clean-css/clean-css/issues/595) - more relaxed block matching.
* Fixed issue [#601](https://github.com/clean-css/clean-css/issues/601) - percentage minifying inside `flex`.
* Fixed issue [#602](https://github.com/clean-css/clean-css/issues/602) - backslash IE hacks after a space.

[3.3.1 / 2015-06-02](https://github.com/clean-css/clean-css/compare/v3.3.0...v3.3.1)
==================

* Fixed issue [#590](https://github.com/clean-css/clean-css/issues/590) - edge case in `@import` processing.

[3.3.0 / 2015-05-31](https://github.com/clean-css/clean-css/compare/v3.2.11...v3.3.0)
==================

* Cleans up url rebase code getting rid of unnecessary state.
* Cleans up tokenizer code getting rid of unnecessary state.
* Moves source maps tracker into lib/source-maps/track.
* Moves tokenizer code into lib/tokenizer.
* Moves URL scanner into lib/urls/reduce (was named incorrectly before).
* Moves URL rebasing & rewriting into lib/urls.
* Fixed issue [#375](https://github.com/clean-css/clean-css/issues/375) - unit compatibility switches.
* Fixed issue [#436](https://github.com/clean-css/clean-css/issues/436) - refactors URI rewriting.
* Fixed issue [#448](https://github.com/clean-css/clean-css/issues/448) - rebasing no protocol URIs.
* Fixed issue [#517](https://github.com/clean-css/clean-css/issues/517) - turning off color optimizations.
* Fixed issue [#542](https://github.com/clean-css/clean-css/issues/542) - space after closing brace in IE.
* Fixed issue [#562](https://github.com/clean-css/clean-css/issues/562) - optimizing invalid color values.
* Fixed issue [#563](https://github.com/clean-css/clean-css/issues/563) - `background:inherit` restoring.
* Fixed issue [#570](https://github.com/clean-css/clean-css/issues/570) - rebasing "no-url()" imports.
* Fixed issue [#574](https://github.com/clean-css/clean-css/issues/574) - rewriting internal URLs.
* Fixed issue [#575](https://github.com/clean-css/clean-css/issues/575) - missing directory as a `target`.
* Fixed issue [#577](https://github.com/clean-css/clean-css/issues/577) - `background-clip` into shorthand.
* Fixed issue [#579](https://github.com/clean-css/clean-css/issues/579) - `background-origin` into shorthand.
* Fixed issue [#580](https://github.com/clean-css/clean-css/issues/580) - mixed `@import` processing.
* Fixed issue [#582](https://github.com/clean-css/clean-css/issues/582) - overriding with prefixed values.
* Fixed issue [#583](https://github.com/clean-css/clean-css/issues/583) - URL quoting for SVG data.
* Fixed issue [#587](https://github.com/clean-css/clean-css/issues/587) - too aggressive `border` reordering.

[3.2.11 / 2015-05-31](https://github.com/clean-css/clean-css/compare/v3.2.10...v3.2.11)
==================

* Fixed issue [#563](https://github.com/clean-css/clean-css/issues/563) - `background:inherit` restoring.
* Fixed issue [#582](https://github.com/clean-css/clean-css/issues/582) - overriding with prefixed values.
* Fixed issue [#583](https://github.com/clean-css/clean-css/issues/583) - URL quoting for SVG data.
* Fixed issue [#587](https://github.com/clean-css/clean-css/issues/587) - too aggressive `border` reordering.

[3.2.10 / 2015-05-14](https://github.com/clean-css/clean-css/compare/v3.2.9...v3.2.10)
==================

* Fixed issue [#572](https://github.com/clean-css/clean-css/issues/572) - empty elements removal.

[3.2.9 / 2015-05-08](https://github.com/clean-css/clean-css/compare/v3.2.8...v3.2.9)
==================

* Fixed issue [#567](https://github.com/clean-css/clean-css/issues/567) - merging colors as functions.

[3.2.8 / 2015-05-04](https://github.com/clean-css/clean-css/compare/v3.2.7...v3.2.8)
==================

* Fixed issue [#561](https://github.com/clean-css/clean-css/issues/561) - restructuring special selectors.

[3.2.7 / 2015-05-03](https://github.com/clean-css/clean-css/compare/v3.2.6...v3.2.7)
==================

* Fixed issue [#551](https://github.com/clean-css/clean-css/issues/551) - edge case in restructuring.
* Fixed issue [#553](https://github.com/clean-css/clean-css/issues/553) - another style of SVG fallback.
* Fixed issue [#558](https://github.com/clean-css/clean-css/issues/558) - units in same selector merging.

[3.2.6 / 2015-04-28](https://github.com/clean-css/clean-css/compare/v3.2.5...v3.2.6)
==================

* Fixed issue [#550](https://github.com/clean-css/clean-css/issues/550) - proper `contentSources` tracking.
* Fixed issue [#556](https://github.com/clean-css/clean-css/issues/556) - regression in IE backslash hacks.

[3.2.5 / 2015-04-25](https://github.com/clean-css/clean-css/compare/v3.2.4...v3.2.5)
==================

* Fixed issue [#543](https://github.com/clean-css/clean-css/issues/543) - better "comment in body" handling.
* Fixed issue [#548](https://github.com/clean-css/clean-css/issues/548) - regression in font minifying.
* Fixed issue [#549](https://github.com/clean-css/clean-css/issues/549) - special comments in source maps.

[3.2.4 / 2015-04-24](https://github.com/clean-css/clean-css/compare/v3.2.3...v3.2.4)
==================

* Fixed issue [#544](https://github.com/clean-css/clean-css/issues/544) - regression in same value merging.
* Fixed issue [#546](https://github.com/clean-css/clean-css/issues/546) - IE<11 `calc()` issue.

[3.2.3 / 2015-04-22](https://github.com/clean-css/clean-css/compare/v3.2.2...v3.2.3)
==================

* Fixed issue [#541](https://github.com/clean-css/clean-css/issues/541) - `outline-style:auto` in shorthand.

[3.2.2 / 2015-04-21](https://github.com/clean-css/clean-css/compare/v3.2.1...v3.2.2)
==================

* Fixed issue [#537](https://github.com/clean-css/clean-css/issues/537) - regression in simple optimizer.

[3.2.1 / 2015-04-20](https://github.com/clean-css/clean-css/compare/v3.2.0...v3.2.1)
==================

* Fixed issue [#534](https://github.com/clean-css/clean-css/issues/534) - wrong `@font-face` stringifying.

[3.2.0 / 2015-04-19](https://github.com/clean-css/clean-css/compare/v3.1.9...v3.2.0)
==================

* Bumps commander to 2.8.x.
* Fixes remote asset rebasing when passing data as a hash.
* Improves path resolution inside source maps.
* Makes `root` option implicitely default to `process.cwd()`.
* Fixed issue [#371](https://github.com/clean-css/clean-css/issues/371) - `background` fallback with `none`.
* Fixed issue [#376](https://github.com/clean-css/clean-css/issues/376) - option to disable `0[unit]` -> `0`.
* Fixed issue [#396](https://github.com/clean-css/clean-css/issues/396) - better input source maps tracking.
* Fixed issue [#397](https://github.com/clean-css/clean-css/issues/397) - support for source map sources.
* Fixed issue [#399](https://github.com/clean-css/clean-css/issues/399) - support compacting with source maps.
* Fixed issue [#429](https://github.com/clean-css/clean-css/issues/429) - unifies data tokenization.
* Fixed issue [#446](https://github.com/clean-css/clean-css/issues/446) - `list-style` fuzzy matching.
* Fixed issue [#468](https://github.com/clean-css/clean-css/issues/468) - bumps `source-map` to 0.4.x.
* Fixed issue [#480](https://github.com/clean-css/clean-css/issues/480) - extracting uppercase property names.
* Fixed issue [#487](https://github.com/clean-css/clean-css/issues/487) - source map paths under Windows.
* Fixed issue [#490](https://github.com/clean-css/clean-css/issues/490) - vendor prefixed multivalue `background`.
* Fixed issue [#500](https://github.com/clean-css/clean-css/issues/500) - merging duplicate adjacent properties.
* Fixed issue [#504](https://github.com/clean-css/clean-css/issues/504) - keeping `url()` quotes.
* Fixed issue [#507](https://github.com/clean-css/clean-css/issues/507) - merging longhands into many shorthands.
* Fixed issue [#508](https://github.com/clean-css/clean-css/issues/508) - removing duplicate media queries.
* Fixed issue [#521](https://github.com/clean-css/clean-css/issues/521) - unit optimizations inside `calc()`.
* Fixed issue [#524](https://github.com/clean-css/clean-css/issues/524) - timeouts in `@import` inlining.
* Fixed issue [#526](https://github.com/clean-css/clean-css/issues/526) - shorthand overriding into a function.
* Fixed issue [#528](https://github.com/clean-css/clean-css/issues/528) - better support for IE<9 hacks.
* Fixed issue [#529](https://github.com/clean-css/clean-css/issues/529) - wrong font weight minification.

[3.1.9 / 2015-04-04](https://github.com/clean-css/clean-css/compare/v3.1.8...v3.1.9)
==================

* Fixes issue [#511](https://github.com/clean-css/clean-css/issues/511) - `)` advanced processing.

[3.1.8 / 2015-03-17](https://github.com/clean-css/clean-css/compare/v3.1.7...v3.1.8)
==================

* Fixes issue [#498](https://github.com/clean-css/clean-css/issues/498) - reordering and flexbox.
* Fixes issue [#499](https://github.com/clean-css/clean-css/issues/499) - too aggressive `-` removal.

[3.1.7 / 2015-03-16](https://github.com/clean-css/clean-css/compare/v3.1.6...v3.1.7)
==================

* Backports fix to [#480](https://github.com/clean-css/clean-css/issues/480) - reordering and uppercase properties.
* Fixes issue [#496](https://github.com/clean-css/clean-css/issues/496) - space after bracket removal.

[3.1.6 / 2015-03-12](https://github.com/clean-css/clean-css/compare/v3.1.5...v3.1.6)
==================

* Fixes issue [#489](https://github.com/clean-css/clean-css/issues/489) - `AlphaImageLoader` IE filter.

[3.1.5 / 2015-03-06](https://github.com/clean-css/clean-css/compare/v3.1.4...v3.1.5)
==================

* Fixes issue [#483](https://github.com/clean-css/clean-css/issues/483) - property order in restructuring.

[3.1.4 / 2015-03-04](https://github.com/clean-css/clean-css/compare/v3.1.3...v3.1.4)
==================

* Fixes issue [#472](https://github.com/clean-css/clean-css/issues/472) - broken function minification.
* Fixes issue [#477](https://github.com/clean-css/clean-css/issues/477) - `@import`s order in restructuring.
* Fixes issue [#478](https://github.com/clean-css/clean-css/issues/478) - ultimate fix to brace whitespace.

[3.1.3 / 2015-03-03](https://github.com/clean-css/clean-css/compare/v3.1.2...v3.1.3)
==================

* Fixes issue [#464](https://github.com/clean-css/clean-css/issues/464) - data URI with quoted braces.
* Fixes issue [#475](https://github.com/clean-css/clean-css/issues/475) - whitespace after closing brace.

[3.1.2 / 2015-03-01](https://github.com/clean-css/clean-css/compare/v3.1.1...v3.1.2)
==================

* Refixed issue [#471](https://github.com/clean-css/clean-css/issues/471) - correct order after restructuring.
* Fixes issue [#466](https://github.com/clean-css/clean-css/issues/466) - rebuilding background shorthand.
* Fixes issue [#462](https://github.com/clean-css/clean-css/issues/462) - escaped apostrophes in selectors.

[3.1.1 / 2015-02-27](https://github.com/clean-css/clean-css/compare/v3.1.0...v3.1.1)
==================

* Fixed issue [#469](https://github.com/clean-css/clean-css/issues/469) - extracting broken property.
* Fixed issue [#470](https://github.com/clean-css/clean-css/issues/470) - negative padding removal.
* Fixed issue [#471](https://github.com/clean-css/clean-css/issues/471) - correct order after restructuring.

[3.1.0 / 2015-02-26](https://github.com/clean-css/clean-css/compare/v3.0.10...v3.1.0)
==================

* Adds `0deg` to `0` minification where possible.
* Adds better non-adjacent selector merging when body is the same.
* Adds official support for node.js 0.12.
* Adds official support for io.js 1.0.
* Adds restructuring optimizations to reorganize selectors, which vastly improves minification.
* Fixed issue [#158](https://github.com/clean-css/clean-css/issues/158) - adds body-based selectors reduction.
* Fixed issue [#182](https://github.com/clean-css/clean-css/issues/182) - removing space after closing brace.
* Fixed issue [#204](https://github.com/clean-css/clean-css/issues/204) - `@media` merging.
* Fixed issue [#351](https://github.com/clean-css/clean-css/issues/351) - remote `@import`s after content.
* Fixed issue [#357](https://github.com/clean-css/clean-css/issues/357) - non-standard but valid URLs.
* Fixed issue [#416](https://github.com/clean-css/clean-css/issues/416) - accepts hash as `minify` argument.
* Fixed issue [#419](https://github.com/clean-css/clean-css/issues/419) - multiple input source maps.
* Fixed issue [#435](https://github.com/clean-css/clean-css/issues/435) - `background-clip` in shorthand.
* Fixed issue [#439](https://github.com/clean-css/clean-css/issues/439) - `background-origin` in shorthand.
* Fixed issue [#442](https://github.com/clean-css/clean-css/issues/442) - space before adjacent `nav`.
* Fixed issue [#445](https://github.com/clean-css/clean-css/issues/445) - regression issue in url processor.
* Fixed issue [#449](https://github.com/clean-css/clean-css/issues/449) - warns of missing close braces.
* Fixed issue [#463](https://github.com/clean-css/clean-css/issues/463) - relative remote `@import` URLs.

[3.0.10 / 2015-02-07](https://github.com/clean-css/clean-css/compare/v3.0.9...v3.0.10)
==================

* Fixed issue [#453](https://github.com/clean-css/clean-css/issues/453) - double `background-repeat`.
* Fixed issue [#455](https://github.com/clean-css/clean-css/issues/455) - property extracting regression.

[3.0.9 / 2015-02-04](https://github.com/clean-css/clean-css/compare/v3.0.8...v3.0.9)
==================

* Fixed issue [#452](https://github.com/clean-css/clean-css/issues/452) - regression in advanced merging.

[3.0.8 / 2015-01-31](https://github.com/clean-css/clean-css/compare/v3.0.7...v3.0.8)
==================

* Fixed issue [#447](https://github.com/clean-css/clean-css/issues/447) - `background-color` in shorthands.
* Fixed issue [#450](https://github.com/clean-css/clean-css/issues/450) - name to hex color converting.

[3.0.7 / 2015-01-22](https://github.com/clean-css/clean-css/compare/v3.0.6...v3.0.7)
==================

* Fixed issue [#441](https://github.com/clean-css/clean-css/issues/441) - hex to name color converting.

[3.0.6 / 2015-01-20](https://github.com/clean-css/clean-css/compare/v3.0.5...v3.0.6)
==================

* Refixed issue [#414](https://github.com/clean-css/clean-css/issues/414) - source maps position fallback.

[3.0.5 / 2015-01-18](https://github.com/clean-css/clean-css/compare/v3.0.4...v3.0.5)
==================

* Fixed issue [#414](https://github.com/clean-css/clean-css/issues/414) - source maps position fallback.
* Fixed issue [#433](https://github.com/clean-css/clean-css/issues/433) - meging `!important` in shorthands.

[3.0.4 / 2015-01-11](https://github.com/clean-css/clean-css/compare/v3.0.3...v3.0.4)
==================

* Fixed issue [#314](https://github.com/clean-css/clean-css/issues/314) - spaces inside `calc`.

[3.0.3 / 2015-01-07](https://github.com/clean-css/clean-css/compare/v3.0.2...v3.0.3)
==================

* Just a version bump as npm incorrectly things 2.2.23 is the latest one.

[3.0.2 / 2015-01-04](https://github.com/clean-css/clean-css/compare/v3.0.1...v3.0.2)
==================

* Fixed issue [#422](https://github.com/clean-css/clean-css/issues/422) - handling `calc` as a unit.

[3.0.1 / 2014-12-19](https://github.com/clean-css/clean-css/compare/v3.0.0...v3.0.1)
==================

* Fixed issue [#410](https://github.com/clean-css/clean-css/issues/410) - advanced merging and comments.
* Fixed issue [#411](https://github.com/clean-css/clean-css/issues/411) - properties and important comments.

[3.0.0 / 2014-12-18](https://github.com/clean-css/clean-css/compare/v2.2.22...v3.0.0)
==================

* Adds more granular control over compatibility settings.
* Adds support for @counter-style at-rule.
* Adds `--source-map`/`sourceMap` switch for building input's source map.
* Adds `--skip-shorthand-compacting`/`shorthandComacting` option for disabling shorthand compacting.
* Allows `target` option to be a path to a folder instead of a file.
* Allows disabling rounding precision. By [@superlukas](https://github.com/superlukas).
* Breaks 2.x compatibility for using CleanCSS as a function.
* Changes `minify` method output to handle multiple outputs.
* Reworks minification to tokenize first then minify.
  See [changes](https://github.com/clean-css/clean-css/compare/b06f37d...dd8c14a).
* Removes support for node.js 0.8.x.
* Renames `noAdvanced` option into `advanced`.
* Renames `noAggressiveMerging` option into `aggressiveMerging`.
* Renames `noRebase` option into `rebase`.
* Speeds up advanced processing by shortening optimize loop.
* Fixed issue [#125](https://github.com/clean-css/clean-css/issues/125) - source maps!
* Fixed issue [#344](https://github.com/clean-css/clean-css/issues/344) - merging `background-size` into shorthand.
* Fixed issue [#352](https://github.com/clean-css/clean-css/issues/352) - honors rebasing in imported stylesheets.
* Fixed issue [#360](https://github.com/clean-css/clean-css/issues/360) - adds 7 extra CSS colors.
* Fixed issue [#363](https://github.com/clean-css/clean-css/issues/363) - `rem` units overriding `px`.
* Fixed issue [#373](https://github.com/clean-css/clean-css/issues/373) - proper `background` shorthand merging.
* Fixed issue [#395](https://github.com/clean-css/clean-css/issues/395) - unescaped brackets in data URIs.
* Fixed issue [#398](https://github.com/clean-css/clean-css/issues/398) - restoring important comments.
* Fixed issue [#400](https://github.com/clean-css/clean-css/issues/400) - API to accept an array of filenames.
* Fixed issue [#403](https://github.com/clean-css/clean-css/issues/403) - tracking input files in source maps.
* Fixed issue [#404](https://github.com/clean-css/clean-css/issues/404) - no state sharing in API.
* Fixed issue [#405](https://github.com/clean-css/clean-css/issues/405) - disables default `background-size` merging.
* Refixed issue [#304](https://github.com/clean-css/clean-css/issues/304) - `background-position` merging.

[2.2.22 / 2014-12-13](https://github.com/clean-css/clean-css/compare/v2.2.21...v2.2.22)
==================

* Backports fix to issue [#304](https://github.com/clean-css/clean-css/issues/304) - `background-position` merging.

[2.2.21 / 2014-12-10](https://github.com/clean-css/clean-css/compare/v2.2.20...v2.2.21)
==================

* Backports fix to issue [#373](https://github.com/clean-css/clean-css/issues/373) - `background` shorthand merging.

[2.2.20 / 2014-12-02](https://github.com/clean-css/clean-css/compare/v2.2.19...v2.2.20)
==================

* Backports fix to issue [#390](https://github.com/clean-css/clean-css/issues/390) - pseudo-class merging.

[2.2.19 / 2014-11-20](https://github.com/clean-css/clean-css/compare/v2.2.18...v2.2.19)
==================

* Fixed issue [#385](https://github.com/clean-css/clean-css/issues/385) - edge cases in processing cut off data.

[2.2.18 / 2014-11-17](https://github.com/clean-css/clean-css/compare/v2.2.17...v2.2.18)
==================

* Fixed issue [#383](https://github.com/clean-css/clean-css/issues/383) - rounding fractions once again.

[2.2.17 / 2014-11-09](https://github.com/clean-css/clean-css/compare/v2.2.16...v2.2.17)
==================

* Fixed issue [#380](https://github.com/clean-css/clean-css/issues/380) - rounding fractions to a whole number.

[2.2.16 / 2014-09-16](https://github.com/clean-css/clean-css/compare/v2.2.15...v2.2.16)
==================

* Fixed issue [#359](https://github.com/clean-css/clean-css/issues/359) - handling escaped double backslash.
* Fixed issue [#358](https://github.com/clean-css/clean-css/issues/358) - property merging in compatibility mode.
* Fixed issue [#356](https://github.com/clean-css/clean-css/issues/356) - preserving `*+html` hack.
* Fixed issue [#354](https://github.com/clean-css/clean-css/issues/354) - `!important` overriding in shorthands.

[2.2.15 / 2014-09-01](https://github.com/clean-css/clean-css/compare/v2.2.14...v2.2.15)
==================

* Fixed issue [#343](https://github.com/clean-css/clean-css/issues/343) - too aggressive `rgba`/`hsla` minification.
* Fixed issue [#345](https://github.com/clean-css/clean-css/issues/345) - URL rebasing for document relative ones.
* Fixed issue [#346](https://github.com/clean-css/clean-css/issues/346) - overriding `!important` by `!important`.
* Fixed issue [#350](https://github.com/clean-css/clean-css/issues/350) - edge cases in `@import` processing.

[2.2.14 / 2014-08-25](https://github.com/clean-css/clean-css/compare/v2.2.13...v2.2.14)
==================

* Makes multival operations idempotent.
* Fixed issue [#339](https://github.com/clean-css/clean-css/issues/339) - skips invalid properties.
* Fixed issue [#341](https://github.com/clean-css/clean-css/issues/341) - ensure output is shorter than input.

[2.2.13 / 2014-08-12](https://github.com/clean-css/clean-css/compare/v2.2.12...v2.2.13)
==================

* Fixed issue [#337](https://github.com/clean-css/clean-css/issues/337) - handling component importance.

[2.2.12 / 2014-08-02](https://github.com/clean-css/clean-css/compare/v2.2.11...v2.2.12)
==================

* Fixed issue with tokenizer removing first selector after an unknown @ rule.
* Fixed issue [#329](https://github.com/clean-css/clean-css/issues/329) - `font` shorthands incorrectly processed.
* Fixed issue [#332](https://github.com/clean-css/clean-css/issues/332) - `background` shorthand with colors.
* Refixed issue [#325](https://github.com/clean-css/clean-css/issues/325) - invalid charset declarations.

[2.2.11 / 2014-07-28](https://github.com/clean-css/clean-css/compare/v2.2.10...v2.2.11)
==================

* Fixed issue [#326](https://github.com/clean-css/clean-css/issues/326) - `background-size` regression.

[2.2.10 / 2014-07-27](https://github.com/clean-css/clean-css/compare/v2.2.9...v2.2.10)
==================

* Improved performance of advanced mode validators.
* Fixed issue [#307](https://github.com/clean-css/clean-css/issues/307) - `background-color` in multiple backgrounds.
* Fixed issue [#322](https://github.com/clean-css/clean-css/issues/322) - adds `background-size` support.
* Fixed issue [#323](https://github.com/clean-css/clean-css/issues/323) - stripping variable references.
* Fixed issue [#325](https://github.com/clean-css/clean-css/issues/325) - removing invalid `@charset` declarations.

[2.2.9 / 2014-07-23](https://github.com/clean-css/clean-css/compare/v2.2.8...v2.2.9)
==================

* Adds `background` normalization according to W3C spec.
* Fixed issue [#316](https://github.com/clean-css/clean-css/issues/316) - incorrect `background` processing.

[2.2.8 / 2014-07-14](https://github.com/clean-css/clean-css/compare/v2.2.7...v2.2.8)
==================

* Fixed issue [#313](https://github.com/clean-css/clean-css/issues/313) - processing comment marks in URLs.
* Fixed issue [#315](https://github.com/clean-css/clean-css/issues/315) - `rgba`/`hsla` -> `transparent` in gradients.

[2.2.7 / 2014-07-10](https://github.com/clean-css/clean-css/compare/v2.2.6...v2.2.7)
==================

* Fixed issue [#304](https://github.com/clean-css/clean-css/issues/304) - merging multiple backgrounds.
* Fixed issue [#312](https://github.com/clean-css/clean-css/issues/312) - merging with mixed repeat.

[2.2.6 / 2014-07-05](https://github.com/clean-css/clean-css/compare/v2.2.5...v2.2.6)
==================

* Adds faster quote matching in QuoteScanner.
* Improves QuoteScanner to handle comments correctly.
* Fixed issue [#308](https://github.com/clean-css/clean-css/issues/308) - parsing comments in quoted URLs.
* Fixed issue [#311](https://github.com/clean-css/clean-css/issues/311) - leading/trailing decimal points.

[2.2.5 / 2014-06-29](https://github.com/clean-css/clean-css/compare/v2.2.4...v2.2.5)
==================

* Adds removing extra spaces around / in border-radius.
* Adds replacing same horizontal & vertical value in border-radius.
* Fixed issue [#305](https://github.com/clean-css/clean-css/issues/305) - allows width keywords in `border-width`.

[2.2.4 / 2014-06-27](https://github.com/clean-css/clean-css/compare/v2.2.3...v2.2.4)
==================

* Fixed issue [#301](https://github.com/clean-css/clean-css/issues/301) - proper `border-radius` processing.
* Fixed issue [#303](https://github.com/clean-css/clean-css/issues/303) - correctly preserves viewport units.

[2.2.3 / 2014-06-24](https://github.com/clean-css/clean-css/compare/v2.2.2...v2.2.3)
==================

* Fixed issue [#302](https://github.com/clean-css/clean-css/issues/302) - handling of `outline-style: auto`.

[2.2.2 / 2014-06-18](https://github.com/clean-css/clean-css/compare/v2.2.1...v2.2.2)
==================

* Fixed issue [#297](https://github.com/clean-css/clean-css/issues/297) - `box-shadow` zeros minification.

[2.2.1 / 2014-06-14](https://github.com/clean-css/clean-css/compare/v2.2.0...v2.2.1)
==================

* Fixes new property optimizer for 'none' values.
* Fixed issue [#294](https://github.com/clean-css/clean-css/issues/294) - space after `rgba`/`hsla` in IE<=11.

[2.2.0 / 2014-06-11](https://github.com/clean-css/clean-css/compare/v2.1.8...v2.2.0)
==================

* Adds a better algorithm for quotation marks' removal.
* Adds a better non-adjacent optimizer compatible with the upcoming new property optimizer.
* Adds minifying remote files directly from CLI.
* Adds `--rounding-precision` to control rounding precision.
* Moves quotation matching into a `QuoteScanner` class.
* Adds `npm run browserify` for creating embeddable version of clean-css.
* Fixed list-style-* advanced processing.
* Fixed issue [#134](https://github.com/clean-css/clean-css/issues/134) - merges properties into shorthand form.
* Fixed issue [#164](https://github.com/clean-css/clean-css/issues/164) - removes default values if not needed.
* Fixed issue [#168](https://github.com/clean-css/clean-css/issues/168) - adds better property merging algorithm.
* Fixed issue [#173](https://github.com/clean-css/clean-css/issues/173) - merges same properties if grouped.
* Fixed issue [#184](https://github.com/clean-css/clean-css/issues/184) - uses `!important` for optimization opportunities.
* Fixed issue [#190](https://github.com/clean-css/clean-css/issues/190) - uses shorthand to override another shorthand.
* Fixed issue [#197](https://github.com/clean-css/clean-css/issues/197) - adds borders merging by understandability.
* Fixed issue [#210](https://github.com/clean-css/clean-css/issues/210) - adds temporary workaround for aggressive merging.
* Fixed issue [#246](https://github.com/clean-css/clean-css/issues/246) - removes IE hacks when not in compatibility mode.
* Fixed issue [#247](https://github.com/clean-css/clean-css/issues/247) - removes deprecated `selectorsMergeMode` switch.
* Refixed issue [#250](https://github.com/clean-css/clean-css/issues/250) - based on new quotation marks removal.
* Fixed issue [#257](https://github.com/clean-css/clean-css/issues/257) - turns `rgba`/`hsla` to `transparent` if possible.
* Fixed issue [#265](https://github.com/clean-css/clean-css/issues/265) - adds support for multiple input files.
* Fixed issue [#275](https://github.com/clean-css/clean-css/issues/275) - handling transform properties.
* Fixed issue [#276](https://github.com/clean-css/clean-css/issues/276) - corrects unicode handling.
* Fixed issue [#288](https://github.com/clean-css/clean-css/issues/288) - adds smarter expression parsing.
* Fixed issue [#293](https://github.com/clean-css/clean-css/issues/293) - handles escaped `@` symbols in class names and IDs.

[2.1.8 / 2014-03-28](https://github.com/clean-css/clean-css/compare/v2.1.7...v2.1.8)
==================

* Fixed issue [#267](https://github.com/clean-css/clean-css/issues/267) - incorrect non-adjacent selector merging.

[2.1.7 / 2014-03-24](https://github.com/clean-css/clean-css/compare/v2.1.6...v2.1.7)
==================

* Fixed issue [#264](https://github.com/clean-css/clean-css/issues/264) - `@import` statements inside comments.

[2.1.6 / 2014-03-10](https://github.com/clean-css/clean-css/compare/v2.1.5...v2.1.6)
==================

* Fixed issue [#258](https://github.com/clean-css/clean-css/issues/258) - wrong `@import` handling in `EmptyRemoval`.

[2.1.5 / 2014-03-07](https://github.com/clean-css/clean-css/compare/v2.1.4...v2.1.5)
==================

* Fixed issue [#255](https://github.com/clean-css/clean-css/issues/255) - incorrect processing of a trailing `-0`.

[2.1.4 / 2014-03-01](https://github.com/clean-css/clean-css/compare/v2.1.3...v2.1.4)
==================

* Fixed issue [#250](https://github.com/clean-css/clean-css/issues/250) - correctly handle JSON data in quotations.

[2.1.3 / 2014-02-26](https://github.com/clean-css/clean-css/compare/v2.1.2...v2.1.3)
==================

* Fixed issue [#248](https://github.com/clean-css/clean-css/issues/248) - incorrect merging for vendor selectors.

[2.1.2 / 2014-02-25](https://github.com/clean-css/clean-css/compare/v2.1.1...v2.1.2)
==================

* Fixed issue [#245](https://github.com/clean-css/clean-css/issues/245) - incorrect handling of backslash IE hack.

[2.1.1 / 2014-02-18](https://github.com/clean-css/clean-css/compare/v2.1.0...v2.1.1)
==================

* Adds faster selectors processing in advanced optimizer.
* Fixed issue [#241](https://github.com/clean-css/clean-css/issues/241) - incorrect handling of `:not()` selectors.

[2.1.0 / 2014-02-13](https://github.com/clean-css/clean-css/compare/v2.0.8...v2.1.0)
==================

* Adds an optional callback to minify method.
* Deprecates `--selectors-merge-mode` / `selectorsMergeMode` in favor to `--compatibility` / `compatibility`.
* Fixes debug mode stats for stylesheets using `@import` statements.
* Skips empty removal if advanced processing is enabled.
* Fixed issue [#85](https://github.com/clean-css/clean-css/issues/85) - resolving protocol `@import`s.
* Fixed issue [#160](https://github.com/clean-css/clean-css/issues/160) - re-runs optimizer until a clean pass.
* Fixed issue [#161](https://github.com/clean-css/clean-css/issues/161) - improves tokenizer performance.
* Fixed issue [#163](https://github.com/clean-css/clean-css/issues/163) - round pixels to 2nd decimal place.
* Fixed issue [#165](https://github.com/clean-css/clean-css/issues/165) - extra space after trailing parenthesis.
* Fixed issue [#186](https://github.com/clean-css/clean-css/issues/186) - strip unit from `0rem`.
* Fixed issue [#207](https://github.com/clean-css/clean-css/issues/207) - bug in parsing protocol `@import`s.
* Fixed issue [#213](https://github.com/clean-css/clean-css/issues/213) - faster `rgb` to `hex` transforms.
* Fixed issue [#215](https://github.com/clean-css/clean-css/issues/215) - leading zeros in numerical values.
* Fixed issue [#217](https://github.com/clean-css/clean-css/issues/217) - whitespace inside attribute selectors and URLs.
* Fixed issue [#218](https://github.com/clean-css/clean-css/issues/218) - `@import` statements cleanup.
* Fixed issue [#220](https://github.com/clean-css/clean-css/issues/220) - selector between comments.
* Fixed issue [#223](https://github.com/clean-css/clean-css/issues/223) - two-pass adjacent selectors merging.
* Fixed issue [#226](https://github.com/clean-css/clean-css/issues/226) - don't minify `border:none` to `border:0`.
* Fixed issue [#229](https://github.com/clean-css/clean-css/issues/229) - improved processing of fraction numbers.
* Fixed issue [#230](https://github.com/clean-css/clean-css/issues/230) - better handling of zero values.
* Fixed issue [#235](https://github.com/clean-css/clean-css/issues/235) - IE7 compatibility mode.
* Fixed issue [#236](https://github.com/clean-css/clean-css/issues/236) - incorrect rebasing with nested `import`s.

[2.0.8 / 2014-02-07](https://github.com/clean-css/clean-css/compare/v2.0.7...v2.0.8)
==================

* Fixed issue [#232](https://github.com/clean-css/clean-css/issues/232) - edge case in non-adjacent selectors merging.

[2.0.7 / 2014-01-16](https://github.com/clean-css/clean-css/compare/v2.0.6...v2.0.7)
==================

* Fixed issue [#208](https://github.com/clean-css/clean-css/issues/208) - don't swallow `@page` and `@viewport`.

[2.0.6 / 2014-01-04](https://github.com/clean-css/clean-css/compare/v2.0.5...v2.0.6)
==================

* Fixed issue [#198](https://github.com/clean-css/clean-css/issues/198) - process comments and `@import`s correctly.
* Fixed issue [#205](https://github.com/clean-css/clean-css/issues/205) - freeze on broken `@import` declaration.

[2.0.5 / 2014-01-03](https://github.com/clean-css/clean-css/compare/v2.0.4...v2.0.5)
==================

* Fixed issue [#199](https://github.com/clean-css/clean-css/issues/199) - keep line breaks with no advanced optimizations.
* Fixed issue [#203](https://github.com/clean-css/clean-css/issues/203) - Buffer as a first argument to minify method.

[2.0.4 / 2013-12-19](https://github.com/clean-css/clean-css/compare/v2.0.3...v2.0.4)
==================

* Fixed issue [#193](https://github.com/clean-css/clean-css/issues/193) - HSL color space normalization.

[2.0.3 / 2013-12-18](https://github.com/clean-css/clean-css/compare/v2.0.2...v2.0.3)
==================

* Fixed issue [#191](https://github.com/clean-css/clean-css/issues/191) - leading numbers in `font`/`animation` names.
* Fixed issue [#192](https://github.com/clean-css/clean-css/issues/192) - many `@import`s inside a comment.

[2.0.2 / 2013-11-18](https://github.com/clean-css/clean-css/compare/v2.0.1...v2.0.2)
==================

* Fixed issue [#177](https://github.com/clean-css/clean-css/issues/177) - process broken content correctly.

[2.0.1 / 2013-11-14](https://github.com/clean-css/clean-css/compare/v2.0.0...v2.0.1)
==================

* Fixed issue [#176](https://github.com/clean-css/clean-css/issues/176) - hangs on `undefined` keyword.

[2.0.0 / 2013-11-04](https://github.com/clean-css/clean-css/compare/v1.1.7...v2.0.0)
==================

* Adds simplified and more advanced text escaping / restoring via `EscapeStore` class.
* Adds simplified and much faster empty elements removal.
* Adds missing `@import` processing to our benchmark (run via `npm run bench`).
* Adds CSS tokenizer which will make it possible to optimize content by reordering and/or merging selectors.
* Adds basic optimizer removing duplicate selectors from a list.
* Adds merging duplicate properties within a single selector's body.
* Adds merging adjacent selectors within a scope (single and multiple ones).
* Changes behavior of `--keep-line-breaks`/`keepBreaks` option to keep breaks after trailing braces only.
* Makes all multiple selectors ordered alphabetically (aids merging).
* Adds property overriding so more coarse properties override more granular ones.
* Adds reducing non-adjacent selectors.
* Adds `--skip-advanced`/`noAdvanced` switch to disable advanced optimizations.
* Adds reducing non-adjacent selectors when overridden by more complex selectors.
* Fixed issue [#138](https://github.com/clean-css/clean-css/issues/138) - makes CleanCSS interface OO.
* Fixed issue [#139](https://github.com/clean-css/clean-css/issues/138) - consistent error & warning handling.
* Fixed issue [#145](https://github.com/clean-css/clean-css/issues/145) - debug mode in library too.
* Fixed issue [#157](https://github.com/clean-css/clean-css/issues/157) - gets rid of `removeEmpty` option.
* Fixed issue [#159](https://github.com/clean-css/clean-css/issues/159) - escaped quotes inside content.
* Fixed issue [#162](https://github.com/clean-css/clean-css/issues/162) - strip quotes from Base64 encoded URLs.
* Fixed issue [#166](https://github.com/clean-css/clean-css/issues/166) - `debug` formatting in CLI
* Fixed issue [#167](https://github.com/clean-css/clean-css/issues/167) - `background:transparent` minification.

[1.1.7 / 2013-10-28](https://github.com/clean-css/clean-css/compare/v1.1.6...v1.1.7)
==================

* Fixed issue [#156](https://github.com/clean-css/clean-css/issues/156) - `@import`s inside comments.

[1.1.6 / 2013-10-26](https://github.com/clean-css/clean-css/compare/v1.1.5...v1.1.6)
==================

* Fixed issue [#155](https://github.com/clean-css/clean-css/issues/155) - broken irregular CSS content.

[1.1.5 / 2013-10-24](https://github.com/clean-css/clean-css/compare/v1.1.4...v1.1.5)
==================

* Fixed issue [#153](https://github.com/clean-css/clean-css/issues/153) - `keepSpecialComments` `0`/`1` as a string.

[1.1.4 / 2013-10-23](https://github.com/clean-css/clean-css/compare/v1.1.3...v1.1.4)
==================

* Fixed issue [#152](https://github.com/clean-css/clean-css/issues/152) - adds an option to disable rebasing.

[1.1.3 / 2013-10-04](https://github.com/clean-css/clean-css/compare/v1.1.2...v1.1.3)
==================

* Fixed issue [#150](https://github.com/clean-css/clean-css/issues/150) - minifying `background:none`.

[1.1.2 / 2013-09-29](https://github.com/clean-css/clean-css/compare/v1.1.1...v1.1.2)
==================

* Fixed issue [#149](https://github.com/clean-css/clean-css/issues/149) - shorthand `font` property.

[1.1.1 / 2013-09-07](https://github.com/clean-css/clean-css/compare/v1.1.0...v1.1.1)
==================

* Fixed issue [#144](https://github.com/clean-css/clean-css/issues/144) - skip URLs rebasing by default.

[1.1.0 / 2013-09-06](https://github.com/clean-css/clean-css/compare/v1.0.12...v1.1.0)
==================

* Renamed lib's `debug` option to `benchmark` when doing per-minification benchmarking.
* Added simplified comments processing & imports.
* Fixed issue [#43](https://github.com/clean-css/clean-css/issues/43) - `--debug` switch for minification stats.
* Fixed issue [#65](https://github.com/clean-css/clean-css/issues/65) - full color name / hex shortening.
* Fixed issue [#84](https://github.com/clean-css/clean-css/issues/84) - support for `@import` with media queries.
* Fixed issue [#124](https://github.com/clean-css/clean-css/issues/124) - raise error on broken imports.
* Fixed issue [#126](https://github.com/clean-css/clean-css/issues/126) - proper CSS expressions handling.
* Fixed issue [#129](https://github.com/clean-css/clean-css/issues/129) - rebasing imported URLs.
* Fixed issue [#130](https://github.com/clean-css/clean-css/issues/130) - better code modularity.
* Fixed issue [#135](https://github.com/clean-css/clean-css/issues/135) - require node.js 0.8+.

[1.0.12 / 2013-07-19](https://github.com/clean-css/clean-css/compare/v1.0.11...v1.0.12)
===================

* Fixed issue [#121](https://github.com/clean-css/clean-css/issues/121) - ability to skip `@import` processing.

[1.0.11 / 2013-07-08](https://github.com/clean-css/clean-css/compare/v1.0.10...v1.0.11)
===================

* Fixed issue [#117](https://github.com/clean-css/clean-css/issues/117) - line break escaping in comments.

[1.0.10 / 2013-06-13](https://github.com/clean-css/clean-css/compare/v1.0.9...v1.0.10)
===================

* Fixed issue [#114](https://github.com/clean-css/clean-css/issues/114) - comments in imported stylesheets.

[1.0.9 / 2013-06-11](https://github.com/clean-css/clean-css/compare/v1.0.8...v1.0.9)
==================

* Fixed issue [#113](https://github.com/clean-css/clean-css/issues/113) - `@import` in comments.

[1.0.8 / 2013-06-10](https://github.com/clean-css/clean-css/compare/v1.0.7...v1.0.8)
==================

* Fixed issue [#112](https://github.com/clean-css/clean-css/issues/112) - reducing `box-shadow` zeros.

[1.0.7 / 2013-06-05](https://github.com/clean-css/clean-css/compare/v1.0.6...v1.0.7)
==================

* Support for `@import` URLs starting with `//`. By [@petetak](https://github.com/petetak).

[1.0.6 / 2013-06-04](https://github.com/clean-css/clean-css/compare/v1.0.5...v1.0.6)
==================

* Fixed issue [#110](https://github.com/clean-css/clean-css/issues/110) - data URIs in URLs.

[1.0.5 / 2013-05-26](https://github.com/clean-css/clean-css/compare/v1.0.4...v1.0.5)
==================

* Fixed issue [#107](https://github.com/clean-css/clean-css/issues/107) - data URIs in imported stylesheets.

1.0.4 / 2013-05-23
==================

* Rewrite relative URLs in imported stylesheets. By [@bluej100](https://github.com/bluej100).

1.0.3 / 2013-05-20
==================

* Support alternative `@import` syntax with file name not wrapped inside `url()` statement.
  By [@bluej100](https://github.com/bluej100).

1.0.2 / 2013-04-29
==================

* Fixed issue [#97](https://github.com/clean-css/clean-css/issues/97) - `--remove-empty` & FontAwesome.

1.0.1 / 2013-04-08
==================

* Do not pick up `bench` and `test` while building `npm` package.
  By [@sindresorhus](https://https://github.com/sindresorhus).

1.0.0 / 2013-03-30
==================

* Fixed issue [#2](https://github.com/clean-css/clean-css/issues/2) - resolving `@import` rules.
* Fixed issue [#44](https://github.com/clean-css/clean-css/issues/44) - examples in `--help`.
* Fixed issue [#46](https://github.com/clean-css/clean-css/issues/46) - preserving special characters in URLs and attributes.
* Fixed issue [#80](https://github.com/clean-css/clean-css/issues/80) - quotation in multi line strings.
* Fixed issue [#83](https://github.com/clean-css/clean-css/issues/83) - HSL to hex color conversions.
* Fixed issue [#86](https://github.com/clean-css/clean-css/issues/86) - broken `@charset` replacing.
* Fixed issue [#88](https://github.com/clean-css/clean-css/issues/88) - removes space in `! important`.
* Fixed issue [#92](https://github.com/clean-css/clean-css/issues/92) - uppercase hex to short versions.

0.10.2 / 2013-03-19
===================

* Fixed issue [#79](https://github.com/clean-css/clean-css/issues/79) - node.js 0.10.x compatibility.

0.10.1 / 2013-02-14
===================

* Fixed issue [#66](https://github.com/clean-css/clean-css/issues/66) - line breaks without extra spaces should
  be handled correctly.

0.10.0 / 2013-02-09
===================

* Switched from [optimist](https://github.com/substack/node-optimist) to
  [commander](https://github.com/visionmedia/commander.js) for CLI processing.
* Changed long options from `--removeempty` to `--remove-empty` and from `--keeplinebreaks` to `--keep-line-breaks`.
* Fixed performance issue with replacing multiple `@charset` declarations and issue
  with line break after `@charset` when using `keepLineBreaks` option. By [@rrjaime](https://github.com/rrjamie).
* Removed Makefile in favor to `npm run` commands (e.g. `make check` -> `npm run check`).
* Fixed issue [#47](https://github.com/clean-css/clean-css/issues/47) - commandline issues on Windows.
* Fixed issue [#49](https://github.com/clean-css/clean-css/issues/49) - remove empty selectors from media query.
* Fixed issue [#52](https://github.com/clean-css/clean-css/issues/52) - strip fraction zeros if not needed.
* Fixed issue [#58](https://github.com/clean-css/clean-css/issues/58) - remove colon where possible.
* Fixed issue [#59](https://github.com/clean-css/clean-css/issues/59) - content property handling.

0.9.1 / 2012-12-19
==================

* Fixed issue [#37](https://github.com/clean-css/clean-css/issues/37) - converting
  `white` and other colors in class names (reported by [@malgorithms](https://github.com/malgorithms)).

0.9.0 / 2012-12-15
==================

* Added stripping quotation from font names (if possible).
* Added stripping quotation from `@keyframes` declaration, `animation` and
  `animation-name` property.
* Added stripping quotations from attributes' value (e.g. `[data-target='x']`).
* Added better hex->name and name->hex color shortening.
* Added `font: normal` and `font: bold` shortening the same way as `font-weight` is.
* Refactored shorthand selectors and added `border-radius`, `border-style`
  and `border-color` shortening.
* Added `margin`, `padding` and `border-width` shortening.
* Added removing line break after commas.
* Fixed removing whitespace inside media query definition.
* Added removing line breaks after a comma, so all declarations are one-liners now.
* Speed optimizations (~10% despite many new features).
* Added [JSHint](https://github.com/jshint/jshint/) validation rules via `make check`.

0.8.3 / 2012-11-29
==================

* Fixed HSL/HSLA colors processing.

0.8.2 / 2012-10-31
==================

* Fixed shortening hex colors and their relation to hashes in URLs.
* Cleanup by [@XhmikosR](https://github.com/XhmikosR).

0.8.1 / 2012-10-28
==================

* Added better zeros processing for `rect(...)` syntax (clip property).

0.8.0 / 2012-10-21
==================

* Added removing URLs quotation if possible.
* Rewrote breaks processing.
* Added `keepBreaks`/`-b` option to keep line breaks in the minimized file.
* Reformatted [lib/clean.js](/lib/clean.js) so it's easier to follow the rules.
* Minimized test data is now minimized with line breaks so it's easier to
  compare the changes line by line.

0.7.0 / 2012-10-14
==================

* Added stripping special comments to CLI (`--s0` and `--s1` options).
* Added stripping special comments to programmatic interface
  (`keepSpecialComments` option).

0.6.0 / 2012-08-05
==================

* Full Windows support with tests (./test.bat).

0.5.0 / 2012-08-02
==================

* Made path to vows local.
* Explicit node.js 0.6 requirement.

0.4.2 / 2012-06-28
==================

* Updated binary `-v` option (version).
* Updated binary to output help when no options given (but not in piped mode).
* Added binary tests.

0.4.1 / 2012-06-10
==================

* Fixed stateless mode where calling `CleanCSS#process` directly was giving
  errors (reported by [@facelessuser](https://github.com/facelessuser)).

0.4.0 / 2012-06-04
==================

* Speed improvements up to 4x thanks to the rewrite of comments and CSS' content
  processing.
* Stripping empty CSS tags is now optional (see [bin/cleancss](/bin/cleancss) for details).
* Improved debugging mode (see [test/bench.js](/test/bench.js))
* Added `make bench` for a one-pass benchmark.

0.3.3 / 2012-05-27
==================

* Fixed tests, [package.json](/package.json) for development, and regex
  for removing empty declarations (thanks to [@vvo](https://github.com/vvo)).

0.3.2 / 2012-01-17
==================

* Fixed output method under node.js 0.6 which incorrectly tried to close
  `process.stdout`.

0.3.1 / 2011-12-16
==================

* Fixed cleaning up `0 0 0 0` expressions.

0.3.0 / 2011-11-29
==================

* Clean-css requires node.js 0.4.0+ to run.
* Removed node.js's 0.2.x 'sys' package dependency
  (thanks to [@jmalonzo](https://github.com/jmalonzo) for a patch).

0.2.6 / 2011-11-27
==================

* Fixed expanding `+` signs in `calc()` when mixed up with adjacent `+` selector.

0.2.5 / 2011-11-27
==================

* Fixed issue with cleaning up spaces inside `calc`/`-moz-calc` declarations
  (thanks to [@cvan](https://github.com/cvan) for reporting it).
* Fixed converting `#f00` to `red` in borders and gradients.

0.2.4 / 2011-05-25
==================

* Fixed problem with expanding `none` to `0` in partial/full background
  declarations.
* Fixed including clean-css library from binary (global to local).

0.2.3 / 2011-04-18
==================

* Fixed problem with optimizing IE filters.

0.2.2 / 2011-04-17
==================

* Fixed problem with space before color in `border` property.

0.2.1 / 2011-03-19
==================

* Added stripping space before `!important` keyword.
* Updated repository location and author information in [package.json](/package.json).

0.2.0 / 2011-03-02
==================

* Added options parsing via optimist.
* Changed code inclusion (thus the version bump).

0.1.0 / 2011-02-27
==================

* First version of clean-css library.
* Implemented all basic CSS transformations.
