<!DOCTYPE html><html id="htmlRoot" lang="zh-CN"><head><meta charset="UTF-8"/><meta content="width=device-width,initial-scale=1" name="viewport"/><title>天玑GPU调速器</title><meta content="Mediatek Mali GPU Governor 控制面板" name="description"/><link href="/favicon.svg" rel="icon" type="image/svg+xml"/><link href="/assets/app-358c279500.css" rel="stylesheet"/><script>// 初始主题设置（与生产一致）
    (function() {
      try {
        const savedTheme = localStorage.getItem('theme');
        const prefersDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const followSystemThemeSetting = localStorage.getItem('followSystemTheme');
        const followSystemTheme = followSystemThemeSetting === null ? true : followSystemThemeSetting === 'true';
        let theme;
        if (followSystemTheme) theme = prefersDarkMode ? 'dark' : 'light';
        else if (savedTheme) theme = savedTheme; else theme = prefersDarkMode ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', theme);
      } catch (e) { console.warn('无法设置初始主题', e); }
    })();</script><script crossorigin src="/assets/index-xrBUoiW9.js" type="module"></script></head><body><div id="loading">加载中</div><div id="app" style="display:none;"><header class="app-header"><div class="header-content"><h1>天玑GPU调速器</h1><div class="theme-toggle" id="themeToggle"><span class="light-icon"><svg fill="none" height="20" viewBox="0 0 24 24" width="20" stroke="currentColor" stroke-width="1.5"><circle cx="12" cy="12" opacity="0.9" r="4" fill="currentColor"/><path d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M6.34 6.34L4.93 4.93M19.07 19.07l-1.41-1.41" opacity="0.8" stroke-linecap="round"/><circle cx="12" cy="12" opacity="0.3" r="6.5" fill="none" stroke="currentColor" stroke-dasharray="1,3" stroke-width="0.5"/></svg> </span><span class="dark-icon"><svg fill="currentColor" height="20" viewBox="0 0 24 24" width="20"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" opacity="0.9"/><circle cx="15" cy="8" opacity="0.4" r="1"/><circle cx="17" cy="11" opacity="0.3" r="0.8"/><circle cx="14.5" cy="13" opacity="0.4" r="0.6"/><path d="M8 4l0.5 1.5L10 6l-1.5 0.5L8 8l-0.5-1.5L6 6l1.5-0.5z" opacity="0.6"/><path d="M5 9l0.3 0.9L6.2 10l-0.9 0.3L5 11.2l-0.3-0.9L3.8 10l0.9-0.3z" opacity="0.5"/></svg></span></div></div></header><div id="header-container"></div><main class="container"><div class="page-container"><div class="page" id="page-status"><section class="card" id="appIconCard" tabindex="0"><div class="card-content app-icon-content"><div class="miuix-app-icon"><div class="icon-background"><svg fill="currentColor" height="40" viewBox="0 0 24 24" width="40" class="app-main-icon"><path d="M12 2L14.09 8.26L22 9L16 14.74L17.18 22.02L12 18.77L6.82 22.02L8 14.74L2 9L9.91 8.26L12 2Z" opacity="0.3"/><path d="M12 2L14.09 8.26L22 9L16 14.74L17.18 22.02L12 18.77L6.82 22.02L8 14.74L2 9L9.91 8.26L12 2Z"/><circle cx="12" cy="12" opacity="0.8" r="3" fill="white"/><path d="M12 8v8M8 12h8" opacity="0.6" stroke="white" stroke-width="1.5" stroke-linecap="round"/></svg></div><div class="app-info"><h1 class="app-title" data-i18n="header_title">天玑GPU调速器</h1><p class="app-subtitle">Mediatek Mali GPU Governor</p></div></div></div></section><section class="card" id="statusCard"><h2 class="card-title"><span class="title-icon"><svg fill="currentColor" height="22" viewBox="0 0 24 24" width="22"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 15l-4-4 1.41-1.41L11 14.17l5.59-5.59L18 10l-7 7z"/></svg> </span><span data-i18n="status_title">模块状态</span></h2><div class="card-content"><div class="status-item"><span><div class="miuix-status-icon"><svg fill="currentColor" height="14" viewBox="0 0 24 24" width="14" class="status-icon"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" opacity="0.3"/><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2z"/><circle cx="12" cy="12" opacity="0.9" r="3" fill="white"/><path d="M12 8v8M8 12h8" opacity="0.7" stroke="white" stroke-width="1.2" stroke-linecap="round"/></svg></div><span class="status-text" data-i18n="status_running">运行状态:</span> </span><span class="status-badge" data-i18n="status_checking" id="runningStatus">检查中...</span></div><div class="status-item"><span><div class="miuix-status-icon"><svg fill="currentColor" height="14" viewBox="0 0 24 24" width="14" class="status-icon"><path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" opacity="0.3"/><path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z"/><circle cx="12" cy="12" opacity="0.9" r="2" fill="white"/></svg></div><span class="status-text" data-i18n="status_current_mode">当前模式:</span> </span><span class="mode-badge default" data-i18n="status_mode_unknown" id="currentMode">加载中...</span></div><div class="status-item"><span><div class="miuix-status-icon"><svg fill="currentColor" height="14" viewBox="0 0 24 24" width="14" class="status-icon"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z" opacity="0.3"/><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/><path d="M8 12h8M8 14h6M8 16h4" opacity="0.7" stroke="white" stroke-width="1"/><circle cx="15.5" cy="6.5" opacity="0.8" r="1" fill="white"/></svg></div><span class="status-text" data-i18n="status_module_version">模块版本:</span> </span><span class="version-badge" data-i18n="loading" id="moduleVersion">加载中...</span></div></div></section><section class="card" id="copyrightCard"><div class="card-content copyright-content"><p data-i18n="copyright_text">天玑GPU调速器 © 2025 酷安@瓦力喀 = Github@Seyud</p></div></section></div><div id="status-page-container"></div><div class="page" id="page-config"><section class="card" id="gpuConfigCard"><h2 class="card-title"><span class="title-icon"><svg fill="currentColor" height="22" viewBox="0 0 24 24" width="22"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg> </span><span data-i18n="config_gpu_freq_table">GPU频率表</span></h2><div class="card-content"><div class="table-container"><table id="gpuFreqTable"><thead><tr><th data-i18n="config_freq">频率 (MHz)</th><th data-i18n="config_volt">电压 (uV)</th><th data-i18n="config_ddr">内存档位</th><th data-i18n="config_edit">编辑</th></tr></thead><tbody><tr><td class="loading-text" colspan="4" data-i18n="status_loading">加载中...</td></tr></tbody></table></div><div class="config-actions"><button class="btn btn-miuix" id="addConfigBtn"><span class="btn-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z"/></svg> </span><span class="btn-text" data-i18n="config_add">添加配置</span></button> <button class="btn btn-miuix btn-primary" id="saveConfigBtn"><span class="btn-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/></svg> </span><span class="btn-text" data-i18n="config_save">保存配置</span></button></div></div></section><section class="card" id="customConfigCard"><h2 class="card-title"><span class="title-icon"><svg fill="currentColor" height="22" viewBox="0 0 24 24" width="22"><path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/></svg> </span><span data-i18n="config_custom_settings">自定义配置</span></h2><div class="card-content"><div class="config-section"><h3 data-i18n="config_global_settings">全局设置</h3><div class="form-group"><label data-i18n="config_global_mode" for="globalMode">全局模式:</label><div class="custom-select" id="globalModeContainer"><div class="selected-option" id="selectedGlobalMode"><span data-i18n="config_mode_balance">平衡模式</span></div><div class="options-container" id="globalModeOptions"><div class="option" data-i18n="config_mode_powersave" data-value="powersave">省电模式</div><div class="option" data-i18n="config_mode_balance" data-value="balance">平衡模式</div><div class="option" data-i18n="config_mode_performance" data-value="performance">性能模式</div><div class="option" data-i18n="config_mode_fast" data-value="fast">极速模式</div></div></div><select class="form-control" id="globalMode" style="display: none;"></select></div><div class="form-group"><label data-i18n="config_idle_threshold" for="idleThreshold">空闲阈值 (%):</label> <input id="idleThreshold" type="number" class="form-control" max="100" min="0"></div></div><div class="settings-tabs-grid mode-tabs-grid"><button class="settings-tab-btn active" data-i18n="config_mode_powersave" data-mode="powersave">省电模式</button> <button class="settings-tab-btn" data-i18n="config_mode_balance" data-mode="balance">平衡模式</button> <button class="settings-tab-btn" data-i18n="config_mode_performance" data-mode="performance">性能模式</button> <button class="settings-tab-btn" data-i18n="config_mode_fast" data-mode="fast">极速模式</button></div><div class="mode-config-container"><div class="mode-config-section active" id="powersave-config"><h3 data-i18n="config_powersave_mode">省电模式设置</h3><div class="form-grid"><div class="form-group"><label data-i18n="config_margin" for="powersaveMargin">余量 (%):</label> <input id="powersaveMargin" type="number" class="form-control" max="100" min="0"></div><div class="form-group"><label data-i18n="config_sampling_interval" for="powersaveSamplingInterval">采样间隔 (ms):</label> <input id="powersaveSamplingInterval" type="number" class="form-control" max="100" min="1"></div><div class="form-group"><label data-i18n="config_min_adaptive_interval" for="powersaveMinAdaptiveInterval">最小自适应间隔 (ms):</label> <input id="powersaveMinAdaptiveInterval" type="number" class="form-control" max="50" min="1"></div><div class="form-group"><label data-i18n="config_max_adaptive_interval" for="powersaveMaxAdaptiveInterval">最大自适应间隔 (ms):</label> <input id="powersaveMaxAdaptiveInterval" type="number" class="form-control" max="50" min="1"></div><div class="form-group"><label data-i18n="config_up_rate_delay" for="powersaveUpRateDelay">升频延迟 (ms):</label> <input id="powersaveUpRateDelay" type="number" class="form-control" max="10000" min="0"></div><div class="form-group"><label data-i18n="config_down_rate_delay" for="powersaveDownRateDelay">降频延迟 (ms):</label> <input id="powersaveDownRateDelay" type="number" class="form-control" max="10000" min="0"></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_aggressive_down">激进降频策略</div></div><div class="miuix-switch"><input id="powersaveAggressiveDown" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_gaming_mode">游戏优化</div></div><div class="miuix-switch"><input id="powersaveGamingMode" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_adaptive_sampling">自适应采样</div></div><div class="miuix-switch"><input id="powersaveAdaptiveSampling" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div></div><div class="mode-config-section" id="balance-config"><h3 data-i18n="config_balance_mode">平衡模式设置</h3><div class="form-grid"><div class="form-group"><label data-i18n="config_margin" for="balanceMargin">余量 (%):</label> <input id="balanceMargin" type="number" class="form-control" max="100" min="0"></div><div class="form-group"><label data-i18n="config_sampling_interval" for="balanceSamplingInterval">采样间隔 (ms):</label> <input id="balanceSamplingInterval" type="number" class="form-control" max="100" min="1"></div><div class="form-group"><label data-i18n="config_min_adaptive_interval" for="balanceMinAdaptiveInterval">最小自适应间隔 (ms):</label> <input id="balanceMinAdaptiveInterval" type="number" class="form-control" max="50" min="1"></div><div class="form-group"><label data-i18n="config_max_adaptive_interval" for="balanceMaxAdaptiveInterval">最大自适应间隔 (ms):</label> <input id="balanceMaxAdaptiveInterval" type="number" class="form-control" max="50" min="1"></div><div class="form-group"><label data-i18n="config_up_rate_delay" for="balanceUpRateDelay">升频延迟 (ms):</label> <input id="balanceUpRateDelay" type="number" class="form-control" max="10000" min="0"></div><div class="form-group"><label data-i18n="config_down_rate_delay" for="balanceDownRateDelay">降频延迟 (ms):</label> <input id="balanceDownRateDelay" type="number" class="form-control" max="10000" min="0"></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_aggressive_down">激进降频策略</div></div><div class="miuix-switch"><input id="balanceAggressiveDown" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_gaming_mode">游戏优化</div></div><div class="miuix-switch"><input id="balanceGamingMode" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_adaptive_sampling">自适应采样</div></div><div class="miuix-switch"><input id="balanceAdaptiveSampling" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div></div><div class="mode-config-section" id="performance-config"><h3 data-i18n="config_performance_mode">性能模式设置</h3><div class="form-grid"><div class="form-group"><label data-i18n="config_margin" for="performanceMargin">余量 (%):</label> <input id="performanceMargin" type="number" class="form-control" max="100" min="0"></div><div class="form-group"><label data-i18n="config_sampling_interval" for="performanceSamplingInterval">采样间隔 (ms):</label> <input id="performanceSamplingInterval" type="number" class="form-control" max="100" min="1"></div><div class="form-group"><label data-i18n="config_min_adaptive_interval" for="performanceMinAdaptiveInterval">最小自适应间隔 (ms):</label> <input id="performanceMinAdaptiveInterval" type="number" class="form-control" max="50" min="1"></div><div class="form-group"><label data-i18n="config_max_adaptive_interval" for="performanceMaxAdaptiveInterval">最大自适应间隔 (ms):</label> <input id="performanceMaxAdaptiveInterval" type="number" class="form-control" max="50" min="1"></div><div class="form-group"><label data-i18n="config_up_rate_delay" for="performanceUpRateDelay">升频延迟 (ms):</label> <input id="performanceUpRateDelay" type="number" class="form-control" max="10000" min="0"></div><div class="form-group"><label data-i18n="config_down_rate_delay" for="performanceDownRateDelay">降频延迟 (ms):</label> <input id="performanceDownRateDelay" type="number" class="form-control" max="10000" min="0"></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_aggressive_down">激进降频策略</div></div><div class="miuix-switch"><input id="performanceAggressiveDown" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_gaming_mode">游戏优化</div></div><div class="miuix-switch"><input id="performanceGamingMode" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_adaptive_sampling">自适应采样</div></div><div class="miuix-switch"><input id="performanceAdaptiveSampling" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div></div><div class="mode-config-section" id="fast-config"><h3 data-i18n="config_fast_mode">极速模式设置</h3><div class="form-grid"><div class="form-group"><label data-i18n="config_margin" for="fastMargin">余量 (%):</label> <input id="fastMargin" type="number" class="form-control" max="100" min="0"></div><div class="form-group"><label data-i18n="config_sampling_interval" for="fastSamplingInterval">采样间隔 (ms):</label> <input id="fastSamplingInterval" type="number" class="form-control" max="100" min="1"></div><div class="form-group"><label data-i18n="config_min_adaptive_interval" for="fastMinAdaptiveInterval">最小自适应间隔 (ms):</label> <input id="fastMinAdaptiveInterval" type="number" class="form-control" max="50" min="1"></div><div class="form-group"><label data-i18n="config_max_adaptive_interval" for="fastMaxAdaptiveInterval">最大自适应间隔 (ms):</label> <input id="fastMaxAdaptiveInterval" type="number" class="form-control" max="50" min="1"></div><div class="form-group"><label data-i18n="config_up_rate_delay" for="fastUpRateDelay">升频延迟 (ms):</label> <input id="fastUpRateDelay" type="number" class="form-control" max="10000" min="0"></div><div class="form-group"><label data-i18n="config_down_rate_delay" for="fastDownRateDelay">降频延迟 (ms):</label> <input id="fastDownRateDelay" type="number" class="form-control" max="10000" min="0"></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_aggressive_down">激进降频策略</div></div><div class="miuix-switch"><input id="fastAggressiveDown" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_gaming_mode">游戏优化</div></div><div class="miuix-switch"><input id="fastGamingMode" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="form-group miuix-super-switch"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="config_adaptive_sampling">自适应采样</div></div><div class="miuix-switch"><input id="fastAdaptiveSampling" type="checkbox" class="miuix-switch-input"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div></div></div><div class="config-actions"><button class="btn btn-miuix" id="loadCustomConfigBtn"><span class="btn-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M14,12L10,8V11H2V13H10V16M20,18V6C20,4.89 19.1,4 18,4H6A2,2 0 0,0 4,6V18A2,2 0 0,0 6,20H18A2,2 0 0,0 20,18Z"/></svg> </span><span class="btn-text" data-i18n="config_load_custom">加载配置</span></button> <button class="btn btn-miuix btn-primary" id="saveCustomConfigBtn"><span class="btn-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/></svg> </span><span class="btn-text" data-i18n="config_save_custom">保存配置</span></button></div></div></section><section class="card" id="gamesCard"><h2 class="card-title"><span class="title-icon"><svg fill="currentColor" height="22" viewBox="0 0 24 24" width="22"><path d="M21,6H3A1,1 0 0,0 2,7V17A1,1 0 0,0 3,18H21A1,1 0 0,0 22,17V7A1,1 0 0,0 21,6M20,16H4V8H20V16M6,15A1,1 0 0,0 7,14A1,1 0 0,0 6,13A1,1 0 0,0 5,14A1,1 0 0,0 6,15M8,13A1,1 0 0,0 9,12A1,1 0 0,0 8,11A1,1 0 0,0 7,12A1,1 0 0,0 8,13M16,15A1,1 0 0,0 17,14A1,1 0 0,0 16,13A1,1 0 0,0 15,14A1,1 0 0,0 16,15M18,13A1,1 0 0,0 19,12A1,1 0 0,0 18,11A1,1 0 0,0 17,12A1,1 0 0,0 18,13Z"/></svg> </span><span data-i18n="config_games_title">游戏列表</span></h2><div class="card-content"><ul class="games-list" id="gamesList"><li class="loading-text" data-i18n="status_loading">加载中...</li></ul><div class="games-actions"><button class="btn" data-i18n="config_games_add" id="addGameBtn">添加游戏</button> <button class="btn" data-i18n="config_games_save" id="saveGamesBtn">保存列表</button></div></div></section></div><div id="config-page-container"></div><div class="page" id="page-log"><section class="card" id="logCard"><h2 class="card-title"><span class="title-icon"><svg fill="currentColor" height="22" viewBox="0 0 24 24" width="22"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg> </span>运行日志</h2><div class="card-content"><div class="log-header"><div class="log-tabs-grid"><button class="log-tab-btn active" data-log="gpu_gov.log"><span class="tab-icon"><svg fill="currentColor" height="20" viewBox="0 0 24 24" width="20"><path d="M3 3v18h18V3H3zm16 16H5V5h14v14zm-8-2h2v2h-2v-2zm0-8h2v6h-2V9z"/></svg> </span><span class="tab-text" data-i18n="log_main">主日志</span></button> <button class="log-tab-btn" data-log="initsvc.log"><span class="tab-icon"><svg fill="currentColor" height="20" viewBox="0 0 24 24" width="20"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg> </span><span class="tab-text" data-i18n="log_init">初始化日志</span></button></div><button class="btn refresh-btn" id="refreshLogBtn"><span class="refresh-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M17.65 6.35A7.958 7.958 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/></svg> </span><span data-i18n="log_refresh">刷新</span></button></div><pre class="log-content" data-i18n="log_loading" id="logContent">加载中...</pre></div></section></div><div id="log-page-container"></div><div class="page" id="page-settings"><section class="card" id="settingsCard"><h2 class="card-title"><span class="title-icon"><svg fill="currentColor" height="22" viewBox="0 0 24 24" width="22"><path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/></svg></span>设置</h2><div class="card-content"><div class="miuix-super-switch" id="followSystemThemeSuperSwitch" aria-describedby="theme-follow-description" aria-label="深色模式跟随系统设置开关"><div class="miuix-super-switch-content"><div class="miuix-super-switch-text"><div class="miuix-super-switch-title" data-i18n="settings_theme_follow">深色模式跟随系统</div><div class="miuix-super-switch-summary" data-i18n="settings_theme_follow_summary" id="theme-follow-description">自动根据系统设置切换主题</div></div><div class="miuix-switch" id="followSystemThemeToggle"><input type="checkbox" class="miuix-switch-input" aria-hidden="true"><div class="miuix-switch-track"><div class="miuix-switch-thumb"></div></div></div></div></div><div class="status-item"><span data-i18n="settings_language">语言设置:</span></div><div class="settings-tabs-grid language-tabs-grid" id="languageContainer"><button class="settings-tab-btn active" data-i18n="settings_language_follow" data-value="system"><span class="tab-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 8.12,16.5 8.91,15.77C9.65,15.09 10.5,14.5 11.44,14.07C10.15,13.29 9.3,11.97 9.3,10.5C9.3,8.26 11.04,6.5 13.3,6.5C15.56,6.5 17.3,8.26 17.3,10.5C17.3,11.97 16.45,13.29 15.16,14.07C16.1,14.5 16.95,15.09 17.69,15.77C18.48,16.5 19.1,17.38 19.53,18.28C18.5,19.36 17.06,20.14 15.44,20.49C14.32,20.72 13.18,20.72 12.06,20.49C10.44,20.14 9,19.36 8.97,18.28H7.07Z"/></svg> </span><span class="tab-text">跟随系统</span></button> <button class="settings-tab-btn" data-i18n="settings_language_zh" data-value="zh"><span class="tab-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M12.87,15.07L11.54,16.4L10.2,15.07L11.54,13.74L12.87,15.07M7.07,12.89L8.4,11.56L9.73,12.89L8.4,14.22L7.07,12.89M16.4,11.56L17.73,12.89L16.4,14.22L15.07,12.89L16.4,11.56M12,8C9.79,8 8,9.79 8,12S9.79,16 12,16 16,14.21 16,12 14.21,8 12,8M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z"/></svg> </span><span class="tab-text">中文</span></button> <button class="settings-tab-btn" data-i18n="settings_language_en" data-value="en"><span class="tab-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M12.87,15.07L11.54,16.4L10.2,15.07L11.54,13.74L12.87,15.07M7.07,12.89L8.4,11.56L9.73,12.89L8.4,14.22L7.07,12.89M16.4,11.56L17.73,12.89L16.4,14.22L15.07,12.89L16.4,11.56M12,8C9.79,8 8,9.79 8,12S9.79,16 12,16 16,14.21 16,12 14.21,8 12,8M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z"/></svg> </span><span class="tab-text">English</span></button></div><div class="setting-description"><small data-i18n="settings_language_desc1">修改语言设置后实时生效</small> <small data-i18n="settings_language_desc2">跟随系统将自动检测系统语言设置</small></div><div class="status-item"><span data-i18n="settings_log_level">主日志等级:</span></div><div class="settings-tabs-grid log-level-tabs-grid" id="logLevelContainer"><button class="settings-tab-btn" data-i18n="settings_log_level_debug" data-value="debug"><span class="tab-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M20,8H17.19C16.74,7.2 16.12,6.5 15.37,5.97L17,4.34L15.66,3L13.69,4.97C13.14,4.8 12.57,4.72 12,4.72C11.43,4.72 10.86,4.8 10.31,4.97L8.34,3L7,4.34L8.63,5.97C7.88,6.5 7.26,7.2 6.81,8H4V9.5H6.09C6.04,9.82 6,10.16 6,10.5H4V12H6C6,12.34 6.04,12.68 6.09,13H4V14.5H6.81C7.85,16.79 10.13,18.5 12.75,18.5C15.37,18.5 17.65,16.79 18.69,14.5H20V13H18.91C18.96,12.68 19,12.34 19,12H21V10.5H19C19,10.16 18.96,9.82 18.91,9.5H20V8M16.5,12C16.5,14.76 14.26,17 11.5,17C8.74,17 6.5,14.76 6.5,12C6.5,9.24 8.74,7 11.5,7C14.26,7 16.5,9.24 16.5,12Z"/></svg> </span><span class="tab-text">Debug</span></button> <button class="settings-tab-btn active" data-i18n="settings_log_level_info" data-value="info"><span class="tab-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"/></svg> </span><span class="tab-text">Info</span></button> <button class="settings-tab-btn" data-i18n="settings_log_level_warn" data-value="warn"><span class="tab-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"/></svg> </span><span class="tab-text">Warn</span></button> <button class="settings-tab-btn" data-i18n="settings_log_level_error" data-value="error"><span class="tab-icon"><svg fill="currentColor" height="16" viewBox="0 0 24 24" width="16"><path d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"/></svg> </span><span class="tab-text">Error</span></button></div><div class="setting-description"><small data-i18n="settings_log_level_desc1">修改日志等级后实时生效</small> <small data-i18n="settings_log_level_desc2">设置为Debug级别将启用详细日志记录</small></div></div></section></div><div id="settings-page-container"></div></div></main><div class="modal" id="editConfigModal"><div class="modal-content"><span class="close-modal">&times;</span><h3 data-i18n="edit_config_title">编辑GPU频率表</h3><div class="form-group"><label data-i18n="edit_config_freq" for="freqInput">频率 (MHz):</label> <input id="freqInput" type="number" max="1000000" min="100000" data-i18n-placeholder="edit_config_freq_example" placeholder="例如: 350000 (KHz)" step="1000"> <small data-i18n="edit_config_freq_hint" class="input-hint">请输入KHz值，表格中显示为MHz</small></div><div class="form-group"><label data-i18n="edit_config_volt" for="voltSelect">电压 (uV):</label><div class="number-spinner"><button class="spinner-btn" id="voltDecreaseBtn">-</button><div class="spinner-value" id="selectedVolt">65000</div><button class="spinner-btn" id="voltIncreaseBtn">+</button> <select class="select" id="voltSelect" style="display: none;"></select></div></div><div class="form-group"><label data-i18n="edit_config_ddr">内存档位:</label><div class="select-container"><div class="custom-select" id="ddrContainer"><div class="selected-option" data-i18n="edit_config_ddr_default" id="selectedDdr">999 (不调整)</div><div class="options-container" id="ddrOptions"><div class="option" data-i18n="edit_config_ddr_default" data-value="999">999 (不调整)</div><div class="option" data-i18n="edit_config_ddr_highest" data-value="0">0 (最高)</div><div class="option" data-i18n="edit_config_ddr_high" data-value="1">1</div><div class="option" data-i18n="edit_config_ddr_medium" data-value="2">2</div><div class="option" data-i18n="edit_config_ddr_low" data-value="3">3</div></div></div></div></div><div class="form-actions"><button class="btn" data-i18n="edit_config_save" id="saveItemBtn">保存</button> <button class="btn btn-secondary" data-i18n="edit_config_cancel" id="cancelEditBtn">取消</button> <button class="btn btn-danger" data-i18n="edit_config_delete" id="deleteItemBtn">删除</button></div></div></div><div class="modal" id="editGameModal"><div class="modal-content"><span class="close-game-modal">&times;</span><h3 data-i18n="edit_game_title">编辑游戏列表</h3><div class="form-group"><label data-i18n="edit_game_package" for="packageNameInput">应用包名:</label> <input id="packageNameInput" placeholder="例如: com.tencent.tmgp.sgame" data-i18n-placeholder="edit_game_package_example"></div><div class="form-group"><label data-i18n="edit_game_mode" for="gameModeSelect">对应模式:</label><div class="select-container"><div class="custom-select" id="gameModeContainer"><div class="selected-option" data-i18n="status_mode_balance" id="selectedGameMode">均衡</div><div class="options-container" id="gameModeOptions"><div class="option" data-i18n="status_mode_powersave" data-value="powersave">省电</div><div class="option" data-i18n="status_mode_balance" data-value="balance">均衡</div><div class="option" data-i18n="status_mode_performance" data-value="performance">性能</div><div class="option" data-i18n="status_mode_fast" data-value="fast">极速</div></div></div><select class="form-select" id="gameModeSelect" style="display: none;"><option data-i18n="status_mode_powersave" value="powersave">省电</option><option data-i18n="status_mode_balance" value="balance" selected="selected">均衡</option><option data-i18n="status_mode_performance" value="performance">性能</option><option data-i18n="status_mode_fast" value="fast">极速</option></select></div></div><div class="form-actions"><button class="btn" data-i18n="edit_game_save" id="saveGameBtn">保存</button> <button class="btn btn-secondary" data-i18n="edit_game_cancel" id="cancelGameBtn">取消</button></div></div></div><div id="modals-container"></div><nav class="nav-bar"><button class="nav-item active" data-page="page-status"><span class="nav-icon"><svg fill="currentColor" height="20" viewBox="0 0 24 24" width="20"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg> </span><span class="nav-text">状态</span></button> <button class="nav-item" data-page="page-config"><span class="nav-icon"><svg fill="currentColor" height="20" viewBox="0 0 24 24" width="20"><path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg> </span><span class="nav-text">配置</span></button> <button class="nav-item" data-page="page-log"><span class="nav-icon"><svg fill="currentColor" height="20" viewBox="0 0 24 24" width="20"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg> </span><span class="nav-text">日志</span></button> <button class="nav-item" data-page="page-settings"><span class="nav-icon"><svg fill="currentColor" height="20" viewBox="0 0 24 24" width="20"><path d="M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10M10,22C9.75,22 9.54,21.82 9.5,21.58L9.13,18.93C8.5,18.68 7.96,18.34 7.44,17.94L4.95,18.95C4.73,19.03 4.46,18.95 4.34,18.73L2.34,15.27C2.21,15.05 2.27,14.78 2.46,14.63L4.57,12.97L4.5,12L4.57,11L2.46,9.37C2.27,9.22 2.21,8.95 2.34,8.73L4.34,5.27C4.46,5.05 4.73,4.96 4.95,5.05L7.44,6.05C7.96,5.66 8.5,5.32 9.13,5.07L9.5,2.42C9.54,2.18 9.75,2 10,2H14C14.25,2 14.46,2.18 14.5,2.42L14.87,5.07C15.5,5.32 16.04,5.66 16.56,6.05L19.05,5.05C19.27,4.96 19.54,5.05 19.66,5.27L21.66,8.73C21.79,8.95 21.73,9.22 21.54,9.37L19.43,11L19.5,12L19.43,13L21.54,14.63C21.73,14.78 21.79,15.05 21.66,15.27L19.66,18.73C19.54,18.95 19.27,19.04 19.05,18.95L16.56,17.95C16.04,18.34 15.5,18.68 14.87,18.93L14.5,21.58C14.46,21.82 14.25,22 14,22H10M11.25,4L10.88,6.61C9.68,6.86 8.62,7.5 7.85,8.39L5.44,7.35L4.69,8.65L6.8,10.2C6.4,11.37 6.4,12.64 6.8,13.8L4.68,15.36L5.43,16.66L7.86,15.62C8.63,16.5 9.68,17.14 10.87,17.38L11.24,20H12.76L13.13,17.39C14.32,17.14 15.37,16.5 16.14,15.62L18.57,16.66L19.32,15.36L17.2,13.81C17.6,12.64 17.6,11.37 17.2,10.2L19.31,8.65L18.56,7.35L16.15,8.39C15.38,7.5 14.32,6.86 13.12,6.62L12.75,4H11.25Z"/></svg> </span><span class="nav-text">设置</span></button></nav><div id="navigation-container"></div></div></body></html>