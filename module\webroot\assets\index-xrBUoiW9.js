(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))s(a);new MutationObserver(a=>{for(const n of a)if(n.type==="childList")for(const i of n.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function t(a){const n={};return a.integrity&&(n.integrity=a.integrity),a.referrerPolicy&&(n.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?n.credentials="include":a.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function s(a){if(a.ep)return;a.ep=!0;const n=t(a);fetch(a.href,n)}})();const f={LOG_PATH:"/data/adb/gpu_governor/log",CONFIG_PATH:"/data/adb/gpu_governor/config/gpu_freq_table.toml",CURRENT_MODE_PATH:"/data/adb/gpu_governor/config/current_mode",GAMES_FILE:"/data/adb/gpu_governor/game/games.toml",LOG_LEVEL_PATH:"/data/adb/gpu_governor/log/log_level",CUSTOM_CONFIG_PATH:"/data/adb/gpu_governor/config/config.toml"},y=[65e3,64375,63750,63125,62500,61875,61250,60625,6e4,59375,58750,58125,57500,56875,56250,55625,55e3,54375,53750,53125,52500,51875,51250,50625,5e4,49375,48750,48125,47500,46875,46250,45625,45e3,44375,43750,43125,42500,41875],_={VOLT_STEP:625,MAX_VOLT:65e3,MIN_VOLT:41875},m={zh:{title:"天玑GPU调速器",loading:"加载中",header_title:"天玑GPU调速器",nav_status:"状态",nav_config:"配置",nav_log:"日志",nav_settings:"设置",status_title:"模块状态",status_running:"运行状态:",status_running_active:"运行中",status_running_inactive:"未运行",status_checking:"检查中...",status_current_mode:"当前模式:",status_mode_powersave:"省电",status_mode_balance:"均衡",status_mode_performance:"性能",status_mode_fast:"极速",status_mode_unknown:"未知",status_module_version:"模块版本:",status_unknown:"未知",config_gpu_title:"GPU频率表",config_freq:"频率 (MHz)",config_volt:"电压 (uV)",config_ddr:"内存档位",config_edit:"编辑",config_loading:"加载中...",config_not_found:"未找到配置",config_add:"添加配置",config_save:"保存配置",config_games_title:"游戏列表",config_games_add:"添加游戏",config_games_save:"保存列表",config_custom_settings:"自定义设置",config_global_settings:"全局设置",config_global_mode:"全局模式:",config_mode_powersave:"省电模式",config_mode_balance:"均衡模式",config_mode_performance:"性能模式",config_mode_fast:"极速模式",config_idle_threshold:"空闲阈值 (%):",config_powersave_mode:"省电模式设置",config_balance_mode:"均衡模式设置",config_performance_mode:"性能模式设置",config_fast_mode:"极速模式设置",config_margin:"余量 (%):",config_sampling_interval:"采样间隔 (ms):",config_min_adaptive_interval:"最小自适应间隔 (ms):",config_max_adaptive_interval:"最大自适应间隔 (ms):",config_up_rate_delay:"升频延迟 (ms):",config_down_rate_delay:"降频延迟 (ms):",config_aggressive_down:"激进降频策略",config_gaming_mode:"游戏优化",config_adaptive_sampling:"自适应采样",config_load_custom:"加载配置",config_save_custom:"保存配置",log_title:"运行日志",log_refresh:"刷新",log_main:"主日志",log_init:"初始化日志",log_loading:"加载中...",log_empty:"日志为空",log_not_found:"未找到日志",log_size_warning:"警告: 日志文件较大",settings_title:"设置",settings_theme_follow:"深色模式跟随系统",settings_theme_follow_summary:"自动根据系统设置切换主题",settings_language:"语言设置:",settings_language_follow:"跟随系统",settings_language_zh:"中文",settings_language_en:"English",settings_language_desc1:"修改语言设置后实时生效",settings_language_desc2:"跟随系统将自动检测系统语言设置",settings_log_level:"主日志等级:",settings_log_level_debug:"Debug (详细)",settings_log_level_info:"Info (信息)",settings_log_level_warn:"Warn (警告)",settings_log_level_error:"Error (错误)",settings_log_level_desc1:"修改日志等级后实时生效",settings_log_level_desc2:"设置为Debug级别将启用详细日志记录",edit_config_title:"编辑GPU频率表",edit_config_freq:"频率 (MHz):",edit_config_freq_example:"例如: 350000 (KHz)",edit_config_freq_hint:"请输入KHz值，表格中显示为MHz",edit_config_volt:"电压 (uV):",edit_config_ddr:"内存档位:",edit_config_ddr_default:"999 (不调整)",edit_config_ddr_highest:"0 (最高)",edit_config_ddr_high:"1",edit_config_ddr_medium:"2",edit_config_ddr_low:"3",edit_config_save:"保存",edit_config_cancel:"取消",edit_config_delete:"删除",edit_game_title:"编辑游戏列表",edit_game_package:"应用包名:",edit_game_package_example:"例如: com.tencent.tmgp.sgame",edit_game_mode:"对应模式:",edit_game_save:"保存",edit_game_cancel:"取消",toast_webui_loaded:"WebUI加载完成",toast_theme_follow_disabled:"已关闭跟随系统主题，现在可以手动切换主题",toast_theme_follow_enabled:"已开启跟随系统主题",toast_theme_follow_keep:"已关闭跟随系统主题，将保持当前主题",toast_theme_switched_dark:"已切换到深色模式",toast_theme_switched_light:"已切换到浅色模式",toast_config_updated:'配置已更新，请点击"保存配置"按钮保存到文件',toast_config_deleted:'配置已删除，请点击"保存配置"按钮保存到文件',toast_config_saved:"配置已成功保存",toast_config_save_fail:"保存配置失败，请检查权限",toast_config_empty:"没有配置可保存",toast_freq_invalid:"请输入有效的频率值",toast_index_invalid:"无效的配置索引",toast_custom_config_loaded:"自定义配置加载完成",toast_game_added:'游戏已添加，请点击"保存列表"按钮保存到文件',toast_game_deleted:'游戏已删除，请点击"保存列表"按钮保存到文件',toast_game_exists:"该应用包名已存在于列表中",toast_game_invalid:"请输入有效的应用包名",toast_games_saved:"游戏列表已成功保存",toast_games_save_fail:"保存游戏列表失败，请检查权限",toast_games_empty:"没有游戏可保存",toast_log_level_debug:"日志等级已设置为: debug，详细日志记录已启用",toast_log_level_set:"日志等级已设置为: {level}，已实时生效",toast_log_level_fail:"保存日志等级失败，请检查权限",toast_language_changed:"语言已切换为{language}",toast_language_follow_system:"语言已设置为跟随系统",copyright_text:"天玑GPU调速器 © 2025 酷安@瓦力喀 = Github@Seyud",config_games_not_found:"未找到游戏",config_games_list_not_found:"未找到游戏列表"},en:{title:"Mediatek Mali GPU Governor",loading:"Loading",header_title:"Mediatek Mali GPU Governor",nav_status:"Status",nav_config:"Config",nav_log:"Log",nav_settings:"Settings",status_title:"Module Status",status_running:"Running Status:",status_running_active:"Running",status_running_inactive:"Not Running",status_checking:"Checking...",status_current_mode:"Current Mode:",status_mode_powersave:"Powersave",status_mode_balance:"Balance",status_mode_performance:"Performance",status_mode_fast:"Fast",status_mode_unknown:"Unknown",status_module_version:"Module Version:",status_unknown:"Unknown",config_gpu_title:"GPU Frequency Table",config_freq:"Frequency (MHz)",config_volt:"Voltage (uV)",config_ddr:"Memory Level",config_edit:"Edit",config_loading:"Loading...",config_not_found:"No configuration found",config_add:"Add Config",config_save:"Save Config",config_games_title:"Games List",config_games_add:"Add Game",config_games_save:"Save List",config_custom_settings:"Custom Settings",config_global_settings:"Global Settings",config_global_mode:"Global Mode:",config_mode_powersave:"Powersave Mode",config_mode_balance:"Balance Mode",config_mode_performance:"Performance Mode",config_mode_fast:"Fast Mode",config_idle_threshold:"Idle Threshold (%):",config_powersave_mode:"Powersave Mode Settings",config_balance_mode:"Balance Mode Settings",config_performance_mode:"Performance Mode Settings",config_fast_mode:"Fast Mode Settings",config_margin:"Margin (%):",config_sampling_interval:"Sampling Interval (ms):",config_min_adaptive_interval:"Min Adaptive Interval (ms):",config_max_adaptive_interval:"Max Adaptive Interval (ms):",config_up_rate_delay:"Up Rate Delay (ms):",config_down_rate_delay:"Down Rate Delay (ms):",config_aggressive_down:"Aggressive Down Strategy",config_gaming_mode:"Gaming Mode",config_adaptive_sampling:"Adaptive Sampling",config_load_custom:"Load Config",config_save_custom:"Save Config",log_title:"Runtime Log",log_refresh:"Refresh",log_main:"Main Log",log_init:"Init Log",log_loading:"Loading...",log_empty:"Log is empty",log_not_found:"Log not found",log_size_warning:"Warning: Log file size exceeds limit, it will be rotated automatically.",settings_title:"Settings",settings_theme_follow:"Follow System Dark Mode",settings_theme_follow_summary:"Automatically switch theme based on system settings",settings_language:"Language:",settings_language_follow:"Follow System",settings_language_zh:"中文",settings_language_en:"English",settings_language_desc1:"Language changes take effect immediately",settings_language_desc2:"Follow System will detect system language settings",settings_log_level:"Main Log Level:",settings_log_level_debug:"Debug (Detailed)",settings_log_level_info:"Info",settings_log_level_warn:"Warn",settings_log_level_error:"Error",settings_log_level_desc1:"Log level changes take effect immediately",settings_log_level_desc2:"Debug level enables detailed logging",edit_config_title:"Edit GPU Frequency Table",edit_config_freq:"Frequency (MHz):",edit_config_freq_example:"e.g.: 350000 (KHz)",edit_config_freq_hint:"Please enter KHz value, displayed as MHz in table",edit_config_volt:"Voltage (uV):",edit_config_ddr:"Memory Level:",edit_config_ddr_default:"999 (No Change)",edit_config_ddr_highest:"0 (Highest)",edit_config_ddr_high:"1",edit_config_ddr_medium:"2",edit_config_ddr_low:"3",edit_config_save:"Save",edit_config_cancel:"Cancel",edit_config_delete:"Delete",edit_game_title:"Edit Games List",edit_game_package:"Package Name:",edit_game_package_example:"e.g.: com.tencent.tmgp.sgame",edit_game_mode:"Corresponding Mode:",edit_game_save:"Save",edit_game_cancel:"Cancel",toast_webui_loaded:"WebUI loaded successfully",toast_theme_follow_disabled:"System theme following disabled, you can now switch themes manually",toast_theme_follow_enabled:"System theme following enabled",toast_theme_follow_keep:"System theme following disabled, current theme will be kept",toast_theme_switched_dark:"Switched to dark mode",toast_theme_switched_light:"Switched to light mode",toast_config_updated:'Configuration updated, please click "Save Config" to save to file',toast_config_deleted:'Configuration deleted, please click "Save Config" to save to file',toast_config_saved:"Configuration saved successfully",toast_config_save_fail:"Failed to save configuration, please check permissions",toast_config_empty:"No configuration to save",toast_freq_invalid:"Please enter a valid frequency value",toast_index_invalid:"Invalid configuration index",toast_custom_config_loaded:"Custom configuration loaded",toast_game_added:'Game added, please click "Save List" button to save to file',toast_game_deleted:'Game deleted, please click "Save List" button to save to file',toast_game_exists:"This package name already exists in the list",toast_game_invalid:"Please enter a valid package name",toast_games_saved:"Games list saved successfully",toast_games_save_fail:"Failed to save games list, please check permissions",toast_games_empty:"No games to save",toast_log_level_debug:"Log level set to: debug, detailed logging has been enabled",toast_log_level_set:"Log level set to: {level}, changes applied immediately",toast_log_level_fail:"Failed to save log level, please check permissions",toast_language_changed:"Language changed to {language}",toast_language_follow_system:"Language set to follow system",copyright_text:"Mediatek Mali GPU Governor © 2025 Coolapk@Walika = Github@Seyud",config_games_not_found:"No games found",config_games_list_not_found:"No games list found"}};function o(r,e={},t="zh"){let a=(m[t]||m.zh)[r]||r;for(const[n,i]of Object.entries(e))a=a.replace(`{${n}}`,String(i));return a}let b=0;function S(r){return`${r}_callback_${Date.now()}_${b++}`}function w(r,e){return typeof e>"u"&&(e={}),new Promise((t,s)=>{const a=S("exec");window[a]=(i,g,c)=>{t({errno:i,stdout:g,stderr:c}),n(a)};function n(i){delete window[i]}try{ksu.exec(r,JSON.stringify(e),a)}catch(i){s(i),n(a)}})}function E(r){ksu.toast(r)}function u(r,e={}){try{return window?.ksu?w(r,e):(console.warn("KernelSU环境不存在，返回模拟数据"),Promise.resolve({errno:0,stdout:"",stderr:""}))}catch(t){return console.error("exec执行失败:",t),Promise.reject(t)}}function l(r){try{window?.ksu?E(r):console.info("[Toast]",r)}catch(e){console.error("Toast 失败:",e)}}function C(r,e,t={}){try{const s=e,a={level:"error",context:r,message:s?.message||String(e),stack:s?.stack,...t};console.error(`[${r}]`,a)}catch(s){console.error("logError failed",s)}}async function L(r,e){try{return{ok:!0,data:await r()}}catch(t){return C(e,t),{ok:!1,error:t,context:e}}}class T{currentLanguage="zh";async loadGpuConfig(){try{const{errno:e,stdout:t}=await u(`cat ${f.CONFIG_PATH}`);if(e===0&&t.trim()){const s=t.trim(),a=[];let n=!1;const g=/freq_table\s*=\s*\[([\s\S]*?)\]/.exec(s);if(g){const c=g[1],d=/\{\s*freq\s*=\s*(\d+),\s*volt\s*=\s*(\d+),\s*ddr_opp\s*=\s*(\d+)\s*\}/g;let h;for(h=d.exec(c);h!==null;){const p=parseInt(h[1],10),v=parseInt(h[2],10),M=parseInt(h[3],10);!Number.isNaN(p)&&!Number.isNaN(v)&&!Number.isNaN(M)&&(a.push({freq:p,volt:v,ddr:M}),n=!0),h=d.exec(c)}}if(!n){const c=/\[\[freq_table\]\][\s\S]*?freq\s*=\s*(\d+)[\s\S]*?volt\s*=\s*(\d+)[\s\S]*?ddr_opp\s*=\s*(\d+)/g;let d;for(d=c.exec(s);d!==null;){const h=parseInt(d[1],10),p=parseInt(d[2],10),v=parseInt(d[3],10);!Number.isNaN(h)&&!Number.isNaN(p)&&!Number.isNaN(v)&&(a.push({freq:h,volt:p,ddr:v}),n=!0),d=c.exec(s)}}return n?(a.sort((c,d)=>c.freq-d.freq),{success:!0,data:a}):{success:!1,error:"config_not_found"}}return{success:!1,error:"config_not_found"}}catch(e){return console.error("加载GPU配置失败:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}async saveGpuConfig(e){if(!e||e.length===0)return l(o("toast_config_empty",{},this.currentLanguage)),{success:!1,error:"empty_config"};const t=[...e].sort((i,g)=>i.freq-g.freq);let s=`# GPU 频率表
# freq 单位: kHz
# volt 单位: uV
# ddr_opp: DDR OPP 档位

`;s+=`freq_table = [
`,t.forEach((i,g)=>{s+=`    { freq = ${i.freq}, volt = ${i.volt}, ddr_opp = ${i.ddr} }${g<t.length-1?",":""}
`}),s+=`]
`;const a=btoa(unescape(encodeURIComponent(s))),n=await L(async()=>await u(`echo '${a}' | base64 -d > ${f.CONFIG_PATH}`),"gpu-config-save");return n.ok?n.data.errno===0?(l(o("toast_config_saved",{},this.currentLanguage)),{success:!0}):(l(o("toast_config_save_fail",{},this.currentLanguage)),{success:!1,error:"save_failed"}):(l(o("toast_config_save_fail",{},this.currentLanguage)),{success:!1,error:"exec_failed"})}async loadCustomConfig(){try{const{errno:e,stdout:t}=await u(`cat ${f.CUSTOM_CONFIG_PATH}`);if(e===0&&t.trim()){const s=t.trim();return{success:!0,data:this.parseCustomConfig(s)}}return{success:!1,error:"config_not_found"}}catch(e){return console.error("加载自定义配置失败:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}async saveCustomConfig(e){const t=this.generateCustomConfigContent(e),s=btoa(unescape(encodeURIComponent(t))),a=await L(async()=>await u(`echo '${s}' | base64 -d > ${f.CUSTOM_CONFIG_PATH}`),"custom-config-save");return a.ok?a.data.errno===0?(l(o("toast_config_saved",{},this.currentLanguage)),{success:!0}):(l(o("toast_config_save_fail",{},this.currentLanguage)),{success:!1,error:"save_failed"}):(l(o("toast_config_save_fail",{},this.currentLanguage)),{success:!1,error:"exec_failed"})}parseCustomConfig(e){const t={},s=/\[global\]([\s\S]*?)(?=\n\[|$)/.exec(e);return s&&(t.global=this.parseSection(s[1])),["powersave","balance","performance","fast"].forEach(n=>{const i=new RegExp(`\\[${n}\\]([\\s\\S]*?)(?=\\n\\[|$)`).exec(e);i&&(t[n]=this.parseSection(i[1]))}),t}parseSection(e){const t={};return e.split(`
`).forEach(a=>{const n=a.trim();if(n&&!n.startsWith("#")){const[i,g]=n.split("=");if(i&&g){const c=i.trim(),d=g.trim().replace(/["']/g,"");d==="true"?t[c]=!0:d==="false"?t[c]=!1:Number.isNaN(Number(d))?t[c]=d:t[c]=Number(d)}}}),t}generateCustomConfigContent(e){let t=`# GPU调速器配置文件

`;t+=`[global]
`,t+=`# 全局模式设置: powersave, balance, performance, fast
`,t+=`mode = "${e.global?.mode||"balance"}"
`,t+=`# 空闲阈值（百分比）
`,t+=`idle_threshold = ${e.global?.idle_threshold??5}

`;const s=["powersave","balance","performance","fast"],a={powersave:"省电模式 - 更高的升频阈值，更激进的降频",balance:"平衡模式 - 默认设置",performance:"性能模式 - 更低的升频阈值，更保守的降频",fast:"极速模式 - 最低的升频阈值，最保守的降频"};return s.forEach(n=>{t+=`# ${a[n]}
`,t+=`[${n}]
`,t+=this.generateModeConfigContent(e[n]||{})}),t}generateModeConfigContent(e){let t="";return t+=`# 余量
`,t+=`margin = ${e.margin??10}
`,t+=`# 是否使用激进降频策略
`,t+=`aggressive_down = ${e.aggressive_down?"true":"false"}
`,t+=`# 采样间隔（毫秒）
`,t+=`sampling_interval = ${e.sampling_interval??16}
`,t+=`# 游戏优化 - 启用游戏特殊内存优化
`,t+=`gaming_mode = ${e.gaming_mode?"true":"false"}
`,t+=`# 自适应采样
`,t+=`adaptive_sampling = ${e.adaptive_sampling?"true":"false"}
`,t+=`# 自适应采样最小间隔（毫秒）
`,t+=`min_adaptive_interval = ${e.min_adaptive_interval??4}
`,t+=`# 自适应采样最大间隔（毫秒）
`,t+=`max_adaptive_interval = ${e.max_adaptive_interval??20}
`,t+=`# 升频延迟（毫秒）
`,t+=`up_rate_delay = ${e.up_rate_delay??1e3}
`,t+=`# 降频延迟（毫秒）
`,t+=`down_rate_delay = ${e.down_rate_delay??5e3}

`,t}setLanguage(e){this.currentLanguage=e}}class I{gpuConfigs=[];currentLanguage="zh";gpuFreqTable;addConfigBtn;saveConfigBtn;onEditCallback=null;saveConfigToFile=null;constructor(){this.gpuFreqTable=document.getElementById("gpuFreqTable")?.querySelector("tbody"),this.addConfigBtn=document.getElementById("addConfigBtn"),this.saveConfigBtn=document.getElementById("saveConfigBtn")}init(){this.setupEventListeners()}setupEventListeners(){this.addConfigBtn&&this.addConfigBtn.addEventListener("click",()=>this.openEditModal()),this.saveConfigBtn&&this.saveConfigBtn.addEventListener("click",()=>{this.saveConfigToFile&&this.saveConfigToFile()})}loadConfigs(e){this.gpuConfigs=e||[],this.gpuConfigs.length>0?(this.gpuConfigs.sort((t,s)=>t.freq-s.freq),this.refreshTable()):this.showEmptyState()}refreshTable(){if(this.gpuFreqTable){if(this.gpuFreqTable.innerHTML="",this.gpuConfigs.length===0){this.showEmptyState();return}this.gpuConfigs=[...this.gpuConfigs].sort((e,t)=>e.freq-t.freq),this.gpuConfigs.forEach((e,t)=>{const s=this.createTableRow(e,t);this.gpuFreqTable?.appendChild(s)})}}createTableRow(e,t){const s=document.createElement("tr");s.dataset.index=String(t),s.dataset.freq=String(e.freq);const a=document.createElement("td");a.textContent=(e.freq/1e3).toFixed(0);const n=document.createElement("td");n.textContent=String(e.volt);const i=document.createElement("td");i.textContent=String(e.ddr);const g=document.createElement("td"),c=this.createEditButton(t);return g.appendChild(c),s.appendChild(a),s.appendChild(n),s.appendChild(i),s.appendChild(g),s}createEditButton(e){const t=document.createElement("button");return t.className="edit-btn",t.innerHTML='<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>',t.title="编辑/删除",t.onclick=()=>(this.openEditModal(e),!1),t}showEmptyState(){this.gpuFreqTable&&(this.gpuFreqTable.innerHTML=`<tr><td colspan="4" class="loading-text">${o("config_not_found",{},this.currentLanguage)}</td></tr>`)}openEditModal(e=-1){if(this.onEditCallback){const t=e>=0?this.gpuConfigs[e]:null;this.onEditCallback(t,e)}}addConfig(e){this.gpuConfigs.push(e),this.refreshTable()}updateConfig(e,t){t>=0&&t<this.gpuConfigs.length?this.gpuConfigs[t]=e:this.gpuConfigs.push(e),this.refreshTable()}deleteConfig(e){return e>=0&&e<this.gpuConfigs.length?(this.gpuConfigs.splice(e,1),this.refreshTable(),!0):!1}getConfigs(){return[...this.gpuConfigs]}setEditCallback(e){this.onEditCallback=e}setSaveFileCallback(e){this.saveConfigToFile=e}isFrequencyDuplicate(e,t=-1){return this.gpuConfigs.some((s,a)=>s.freq===e&&a!==t)}getFrequencyRange(){if(this.gpuConfigs.length===0)return{min:0,max:0};const e=this.gpuConfigs.map(t=>t.freq);return{min:Math.min(...e),max:Math.max(...e)}}sortByFrequency(){this.gpuConfigs.sort((e,t)=>e.freq-t.freq),this.refreshTable()}validateConfig(e){const t=[];return(!e.freq||Number.isNaN(e.freq)||e.freq<=0)&&t.push("无效的频率值"),(!e.volt||Number.isNaN(e.volt)||e.volt<=0)&&t.push("无效的电压值"),(e.ddr===void 0||Number.isNaN(e.ddr))&&t.push("无效的DDR档位值"),{isValid:t.length===0,errors:t}}getStatistics(){if(this.gpuConfigs.length===0)return{count:0,avgFreq:0,avgVolt:0,freqRange:{min:0,max:0},voltRange:{min:0,max:0}};const e=this.gpuConfigs.map(s=>s.freq),t=this.gpuConfigs.map(s=>s.volt);return{count:this.gpuConfigs.length,avgFreq:e.reduce((s,a)=>s+a,0)/e.length,avgVolt:t.reduce((s,a)=>s+a,0)/t.length,freqRange:{min:Math.min(...e),max:Math.max(...e)},voltRange:{min:Math.min(...t),max:Math.max(...t)}}}setLanguage(e){this.currentLanguage=e,this.refreshTable()}clearConfigs(){this.gpuConfigs=[],this.refreshTable()}}class k{voltageController;editingIndex=-1;currentLanguage="zh";onSaveCallback=null;onDeleteCallback=null;editConfigModal;closeModalBtn;freqInput;saveItemBtn;cancelEditBtn;deleteItemBtn;ddrContainer;selectedDdr;constructor(e){this.voltageController=e,this.editConfigModal=document.getElementById("editConfigModal"),this.closeModalBtn=document.querySelector(".close-modal"),this.freqInput=document.getElementById("freqInput"),this.saveItemBtn=document.getElementById("saveItemBtn"),this.cancelEditBtn=document.getElementById("cancelEditBtn"),this.deleteItemBtn=document.getElementById("deleteItemBtn"),this.ddrContainer=document.getElementById("ddrContainer"),this.selectedDdr=document.getElementById("selectedDdr")}init(){this.setupEventListeners(),this.setupDdrSelector()}setupEventListeners(){this.closeModalBtn&&this.closeModalBtn.addEventListener("click",()=>this.closeModal()),this.cancelEditBtn&&this.cancelEditBtn.addEventListener("click",()=>this.closeModal()),this.saveItemBtn&&this.saveItemBtn.addEventListener("click",()=>this.saveConfigItem()),this.deleteItemBtn&&this.deleteItemBtn.addEventListener("click",()=>this.deleteConfigItem()),window.addEventListener("click",e=>{e.target===this.editConfigModal&&this.closeModal()}),window.addEventListener("keydown",e=>{e.key==="Escape"&&this.editConfigModal&&this.editConfigModal.style.display==="block"&&this.closeModal()})}setupDdrSelector(){if(!this.ddrContainer)return;this.ddrContainer.addEventListener("click",()=>{this.ddrContainer?.classList.toggle("open"),this.ddrContainer?.classList.contains("open")?setTimeout(()=>{(this.ddrContainer?.querySelectorAll(".option")).forEach((s,a)=>{setTimeout(()=>{s.style.opacity="1",s.style.transform="translateY(0)"},a*50)})},10):((this.ddrContainer?.querySelectorAll(".option")).forEach(s=>{s.style.opacity="0",s.style.transform="translateY(-10px)"}),setTimeout(()=>{this.ddrContainer?.classList.remove("open")},150))});const e=document.querySelectorAll("#ddrOptions .option");e.forEach(t=>{t.addEventListener("click",s=>{s.stopPropagation(),e.forEach(n=>{n.classList.remove("selected")}),t.classList.add("selected"),this.selectedDdr&&(this.selectedDdr.textContent=t.textContent);const a=this.ddrContainer?.querySelectorAll(".option");a.forEach(n=>{n.style.opacity="0",n.style.transform="translateY(-10px)"}),setTimeout(()=>{this.ddrContainer?.classList.remove("open")},150),setTimeout(()=>{a.forEach(n=>{n.style.opacity="",n.style.transform=""})},300)})})}openModal(e=null,t=-1){if(!this.editConfigModal){console.error("模态框元素不存在");return}this.editingIndex=t,e?(this.freqInput&&(this.freqInput.value=String(e.freq)),this.voltageController&&this.voltageController.setVoltage(e.volt),this.setDdrValue(e.ddr),this.deleteItemBtn&&(this.deleteItemBtn.style.display="block")):(this.freqInput&&(this.freqInput.value=""),this.voltageController&&this.voltageController.reset(),this.setDdrValue(999),this.deleteItemBtn&&(this.deleteItemBtn.style.display="none")),this.editConfigModal.style.display="block"}closeModal(){this.editConfigModal&&(this.editConfigModal.style.display="none")}setDdrValue(e){this.selectedDdr&&document.querySelectorAll("#ddrOptions .option").forEach(s=>{parseInt(s.getAttribute("data-value")||"0",10)===e?(this.selectedDdr&&(this.selectedDdr.textContent=s.textContent),s.classList.add("selected")):s.classList.remove("selected")})}getDdrValue(){if(this.selectedDdr){const e=this.selectedDdr.textContent||"999";return parseInt(e.split(" ")[0],10)}return 999}saveConfigItem(){if(!this.freqInput)return;const e=parseInt(this.freqInput.value,10),t=this.voltageController?this.voltageController.getCurrentVoltage():0,s=this.getDdrValue();if(!e||Number.isNaN(e)){l(o("toast_freq_invalid",{},this.currentLanguage));return}const a={freq:e,volt:t,ddr:s};this.onSaveCallback&&this.onSaveCallback(a,this.editingIndex),this.closeModal(),l(o("toast_config_updated",{},this.currentLanguage))}deleteConfigItem(){this.editingIndex>=0?(this.onDeleteCallback&&this.onDeleteCallback(this.editingIndex),this.closeModal(),l(o("toast_config_deleted",{},this.currentLanguage))):l(o("toast_index_invalid",{},this.currentLanguage))}setSaveCallback(e){this.onSaveCallback=e}setDeleteCallback(e){this.onDeleteCallback=e}setLanguage(e){this.currentLanguage=e}}class B{currentLanguage="zh";customConfig={};globalModeSelect;globalModeContainer;selectedGlobalMode;globalModeOptions;idleThresholdInput;powersaveInputs=null;balanceInputs=null;performanceInputs=null;fastInputs=null;constructor(){this.globalModeSelect=document.getElementById("globalMode"),this.globalModeContainer=document.getElementById("globalModeContainer"),this.selectedGlobalMode=document.getElementById("selectedGlobalMode"),this.globalModeOptions=document.getElementById("globalModeOptions"),this.idleThresholdInput=document.getElementById("idleThreshold"),this.powersaveInputs=this.getModeInputs("powersave"),this.balanceInputs=this.getModeInputs("balance"),this.performanceInputs=this.getModeInputs("performance"),this.fastInputs=this.getModeInputs("fast")}getModeInputs(e){return{margin:document.getElementById(`${e}Margin`),sampling_interval:document.getElementById(`${e}SamplingInterval`),min_adaptive_interval:document.getElementById(`${e}MinAdaptiveInterval`),max_adaptive_interval:document.getElementById(`${e}MaxAdaptiveInterval`),up_rate_delay:document.getElementById(`${e}UpRateDelay`),down_rate_delay:document.getElementById(`${e}DownRateDelay`),aggressive_down:document.getElementById(`${e}AggressiveDown`),gaming_mode:document.getElementById(`${e}GamingMode`),adaptive_sampling:document.getElementById(`${e}AdaptiveSampling`)}}init(){this.initGlobalModeSelect(),this.initModeSwitchEvents()}initGlobalModeSelect(){if(!this.selectedGlobalMode||!this.globalModeOptions)return;this.selectedGlobalMode.addEventListener("click",t=>{t.stopPropagation(),this.globalModeContainer?.classList.toggle("open")}),this.globalModeOptions.querySelectorAll(".option").forEach(t=>{t.addEventListener("click",s=>{s.stopPropagation();const a=t.getAttribute("data-value"),n=t.textContent,i=this.selectedGlobalMode?.querySelector("span");i&&(i.textContent=n||"",i.setAttribute("data-i18n",t.getAttribute("data-i18n")||"")),this.globalModeSelect&&a&&(this.globalModeSelect.value=a,this.globalModeSelect.dispatchEvent(new Event("change"))),this.globalModeContainer?.classList.remove("open")})}),document.addEventListener("click",t=>{this.globalModeContainer?.contains(t.target)||this.globalModeContainer?.classList.remove("open")})}initModeSwitchEvents(){const e=document.querySelectorAll(".mode-tabs-grid .settings-tab-btn"),t=document.querySelectorAll(".mode-config-section");this.globalModeSelect&&this.syncModeTabsWithGlobalMode(this.globalModeSelect.value),e.forEach(s=>{s.addEventListener("click",()=>{const a=s.getAttribute("data-mode");e.forEach(i=>{i.classList.remove("active")}),s.classList.add("active"),t.forEach(i=>{i.classList.remove("active")});const n=document.getElementById(`${a}-config`);n&&n.classList.add("active")})}),this.globalModeSelect&&this.globalModeSelect.addEventListener("change",()=>{const s=this.globalModeSelect?.value;s&&this.syncModeTabsWithGlobalMode(s)})}syncModeTabsWithGlobalMode(e){const t=document.querySelectorAll(".mode-tabs-grid .settings-tab-btn"),s=document.querySelectorAll(".mode-config-section");t.forEach(a=>{a.classList.remove("active"),a.getAttribute("data-mode")===e&&a.classList.add("active")}),s.forEach(a=>{a.classList.remove("active"),a.id===`${e}-config`&&a.classList.add("active")})}populateCustomConfigForm(e){this.customConfig=e||{},this.customConfig.global&&(this.globalModeSelect&&this.customConfig.global.mode&&(this.globalModeSelect.value=String(this.customConfig.global.mode)),this.selectedGlobalMode&&this.customConfig.global.mode&&this.updateGlobalModeDisplay(String(this.customConfig.global.mode)),this.idleThresholdInput&&this.customConfig.global.idle_threshold!==void 0&&(this.idleThresholdInput.value=String(this.customConfig.global.idle_threshold))),this.populateModeConfig(this.powersaveInputs,this.customConfig.powersave),this.populateModeConfig(this.balanceInputs,this.customConfig.balance),this.populateModeConfig(this.performanceInputs,this.customConfig.performance),this.populateModeConfig(this.fastInputs,this.customConfig.fast),this.customConfig.global?.mode&&this.syncModeTabsWithGlobalMode(String(this.customConfig.global.mode))}updateGlobalModeDisplay(e){let t="",s="";switch(e){case"powersave":t=o("config_powersave_mode",{},this.currentLanguage),s="config_powersave_mode";break;case"performance":t=o("config_performance_mode",{},this.currentLanguage),s="config_performance_mode";break;case"fast":t=o("config_fast_mode",{},this.currentLanguage),s="config_fast_mode";break;default:t=o("config_balance_mode",{},this.currentLanguage),s="config_balance_mode";break}if(this.selectedGlobalMode){const a=this.selectedGlobalMode.querySelector("span");a&&(a.textContent=t,a.setAttribute("data-i18n",s))}}populateModeConfig(e,t){!t||!e||Object.keys(e).forEach(s=>{const a=e[s];if(a&&t[s]!==void 0){const n=a;n.type==="checkbox"?n.checked=!!t[s]:n.value=String(t[s])}})}getCurrentConfig(){return{global:{mode:this.getGlobalMode(),idle_threshold:this.getIdleThreshold()},powersave:this.getModeConfig(this.powersaveInputs),balance:this.getModeConfig(this.balanceInputs),performance:this.getModeConfig(this.performanceInputs),fast:this.getModeConfig(this.fastInputs)}}getGlobalMode(){if(this.globalModeSelect?.value)return this.globalModeSelect.value;if(this.selectedGlobalMode){const e=this.selectedGlobalMode.querySelector("span");if(e){const t=e.textContent||"";return t.includes("省电")||t.includes("Power Save")?"powersave":t.includes("性能")||t.includes("Performance")?"performance":t.includes("极速")||t.includes("Fast")?"fast":"balance"}}return"balance"}getIdleThreshold(){return this.idleThresholdInput&&this.idleThresholdInput.value||5}getModeConfig(e){if(!e)return{};const t={};return Object.keys(e).forEach(s=>{const a=e[s];a&&(a.type==="checkbox"?t[s]=a.checked:t[s]=a.value?Number(a.value):0)}),t}setLanguage(e){this.currentLanguage=e,this.customConfig.global?.mode&&this.updateGlobalModeDisplay(String(this.customConfig.global.mode))}}class G{currentVoltValue=_.MAX_VOLT;isLongPress=!1;decreaseTimer=null;increaseTimer=null;voltageEventsInitialized=!1;voltSelect;selectedVolt;voltDecreaseBtn;voltIncreaseBtn;constructor(){this.voltSelect=document.getElementById("voltSelect"),this.selectedVolt=document.getElementById("selectedVolt"),this.voltDecreaseBtn=document.getElementById("voltDecreaseBtn"),this.voltIncreaseBtn=document.getElementById("voltIncreaseBtn")}init(){this.initVoltSelect(),this.setupVoltageEvents()}decreaseVolt(){const e=this.currentVoltValue-_.VOLT_STEP;return e>=_.MIN_VOLT?(this.currentVoltValue=e,this.updateVoltDisplay(),!0):!1}increaseVolt(){const e=this.currentVoltValue+_.VOLT_STEP;return e<=_.MAX_VOLT?(this.currentVoltValue=e,this.updateVoltDisplay(),!0):!1}updateVoltDisplay(){if(this.selectedVolt&&(this.selectedVolt.textContent=String(this.currentVoltValue)),this.voltSelect){const e=Array.from(this.voltSelect.options).find(t=>parseInt(t.value,10)===this.currentVoltValue);if(e)this.voltSelect.value=e.value;else{const t=document.createElement("option");t.value=String(this.currentVoltValue),t.textContent=String(this.currentVoltValue),this.voltSelect.appendChild(t),this.voltSelect.value=String(this.currentVoltValue)}this.voltDecreaseBtn&&(this.voltDecreaseBtn.disabled=this.currentVoltValue<=_.MIN_VOLT),this.voltIncreaseBtn&&(this.voltIncreaseBtn.disabled=this.currentVoltValue>=_.MAX_VOLT)}}initVoltSelect(){!this.voltSelect||!this.selectedVolt||(this.voltSelect.innerHTML="",y.forEach(e=>{const t=document.createElement("option");t.value=String(e),t.textContent=String(e),this.voltSelect?.appendChild(t)}),this.currentVoltValue=_.MAX_VOLT,this.selectedVolt.textContent=String(this.currentVoltValue),this.voltSelect.value=String(this.currentVoltValue),this.updateVoltDisplay())}setupVoltageEvents(){this.voltageEventsInitialized||(this.voltDecreaseBtn&&(this.voltDecreaseBtn.addEventListener("click",()=>{if(this.isLongPress){this.isLongPress=!1;return}this.decreaseVolt()}),this.voltDecreaseBtn.addEventListener("mousedown",()=>{this.isLongPress=!1,this.decreaseTimer=setTimeout(()=>{this.isLongPress=!0,this.decreaseVolt()&&this.decreaseTimer&&(this.decreaseTimer=setInterval(()=>{this.decreaseVolt()||this.decreaseTimer&&(clearInterval(this.decreaseTimer),this.decreaseTimer=null)},150))},500)})),this.voltIncreaseBtn&&(this.voltIncreaseBtn.addEventListener("click",()=>{if(this.isLongPress){this.isLongPress=!1;return}this.increaseVolt()}),this.voltIncreaseBtn.addEventListener("mousedown",()=>{this.isLongPress=!1,this.increaseTimer=setTimeout(()=>{this.isLongPress=!0,this.increaseVolt()&&this.increaseTimer&&(this.increaseTimer=setInterval(()=>{this.increaseVolt()||this.increaseTimer&&(clearInterval(this.increaseTimer),this.increaseTimer=null)},150))},500)})),document.addEventListener("mouseup",()=>this.clearTimers()),document.addEventListener("mouseleave",()=>this.clearTimers()),this.setupTouchEvents(),this.voltageEventsInitialized=!0)}setupTouchEvents(){this.voltDecreaseBtn&&this.voltDecreaseBtn.addEventListener("touchstart",e=>{e.preventDefault(),this.isLongPress=!1,this.decreaseVolt(),this.decreaseTimer=setTimeout(()=>{this.isLongPress=!0,this.decreaseTimer=setInterval(()=>{this.decreaseVolt()||this.decreaseTimer&&(clearInterval(this.decreaseTimer),this.decreaseTimer=null)},150)},500)},{passive:!1}),this.voltIncreaseBtn&&this.voltIncreaseBtn.addEventListener("touchstart",e=>{e.preventDefault(),this.isLongPress=!1,this.increaseVolt(),this.increaseTimer=setTimeout(()=>{this.isLongPress=!0,this.increaseTimer=setInterval(()=>{this.increaseVolt()||this.increaseTimer&&(clearInterval(this.increaseTimer),this.increaseTimer=null)},150)},500)},{passive:!1}),document.addEventListener("touchend",()=>this.clearTimers())}clearTimers(){this.decreaseTimer!==null&&(clearTimeout(this.decreaseTimer),clearInterval(this.decreaseTimer),this.decreaseTimer=null),this.increaseTimer!==null&&(clearTimeout(this.increaseTimer),clearInterval(this.increaseTimer),this.increaseTimer=null)}getCurrentVoltage(){return this.currentVoltValue}setVoltage(e){this.currentVoltValue=e,this.updateVoltDisplay()}reset(){this.currentVoltValue=_.MAX_VOLT,this.updateVoltDisplay()}}class x{currentLanguage="zh";voltageController;configFileManager;modalManager;modeConfigManager;gpuConfigManager;loadCustomConfigBtn;saveCustomConfigBtn;constructor(){this.voltageController=new G,this.configFileManager=new T,this.modalManager=new k(this.voltageController),this.modeConfigManager=new B,this.gpuConfigManager=new I,this.loadCustomConfigBtn=document.getElementById("loadCustomConfigBtn"),this.saveCustomConfigBtn=document.getElementById("saveCustomConfigBtn")}init(){this.setupEventListeners(),this.setupModuleCallbacks(),this.initializeModules(),this.loadInitialData()}initializeModules(){this.voltageController.init(),this.modalManager.init(),this.modeConfigManager.init(),this.gpuConfigManager.init()}setupModuleCallbacks(){this.gpuConfigManager.setEditCallback((e,t)=>{this.modalManager.openModal(e,t)}),this.gpuConfigManager.setSaveFileCallback(()=>{this.saveGpuConfigToFile()}),this.modalManager.setSaveCallback((e,t)=>{this.gpuConfigManager.updateConfig(e,t)}),this.modalManager.setDeleteCallback(e=>{this.gpuConfigManager.deleteConfig(e)})}async loadInitialData(){await this.loadGpuConfig(),await this.loadCustomConfigFromFile()}setupEventListeners(){this.loadCustomConfigBtn&&this.loadCustomConfigBtn.addEventListener("click",()=>this.loadCustomConfigFromFile()),this.saveCustomConfigBtn&&this.saveCustomConfigBtn.addEventListener("click",()=>this.saveCustomConfigToFile())}async loadGpuConfig(){const e=await this.configFileManager.loadGpuConfig();e.success&&e.data?this.gpuConfigManager.loadConfigs(e.data):(console.error("加载GPU配置失败:",e.error),this.gpuConfigManager.loadConfigs([]))}async saveGpuConfigToFile(){const e=this.gpuConfigManager.getConfigs();await this.configFileManager.saveGpuConfig(e)}async loadCustomConfigFromFile(){const e=await this.configFileManager.loadCustomConfig();e.success&&e.data?(this.modeConfigManager.populateCustomConfigForm(e.data),l(o("toast_custom_config_loaded",{},this.currentLanguage))):l(o("toast_config_load_fail",{},this.currentLanguage))}async saveCustomConfigToFile(){const e=this.modeConfigManager.getCurrentConfig();await this.configFileManager.saveCustomConfig(e)}setLanguage(e){this.currentLanguage=e,this.configFileManager.setLanguage(e),this.modalManager.setLanguage(e),this.modeConfigManager.setLanguage(e),this.gpuConfigManager.setLanguage(e)}}class V{gamesListData=[];currentLanguage="zh";gamesList;addGameBtn;saveGamesBtn;editGameModal;closeGameModalBtn;packageNameInput;gameModeSelect;saveGameBtn;cancelGameBtn;gameModeContainer;selectedGameMode;gameModeOptions;editingIndex=-1;constructor(){this.gamesList=document.getElementById("gamesList"),this.addGameBtn=document.getElementById("addGameBtn"),this.saveGamesBtn=document.getElementById("saveGamesBtn"),this.editGameModal=document.getElementById("editGameModal"),this.closeGameModalBtn=document.querySelector(".close-game-modal"),this.packageNameInput=document.getElementById("packageNameInput"),this.gameModeSelect=document.getElementById("gameModeSelect"),this.saveGameBtn=document.getElementById("saveGameBtn"),this.cancelGameBtn=document.getElementById("cancelGameBtn"),this.gameModeContainer=document.getElementById("gameModeContainer"),this.selectedGameMode=document.getElementById("selectedGameMode"),this.gameModeOptions=document.getElementById("gameModeOptions")}init(){this.setupEventListeners()}setupEventListeners(){this.addGameBtn&&this.addGameBtn.addEventListener("click",()=>this.openGameModal()),this.saveGamesBtn&&this.saveGamesBtn.addEventListener("click",()=>this.saveGamesToFile()),this.closeGameModalBtn&&this.closeGameModalBtn.addEventListener("click",()=>this.closeGameModal()),this.cancelGameBtn&&this.cancelGameBtn.addEventListener("click",()=>this.closeGameModal()),this.saveGameBtn&&this.saveGameBtn.addEventListener("click",()=>this.saveGameItem()),window.addEventListener("click",e=>{e.target===this.editGameModal&&this.closeGameModal()}),window.addEventListener("keydown",e=>{e.key==="Escape"&&this.editGameModal&&this.editGameModal.style.display==="block"&&this.closeGameModal()}),this.initGameModeSelect()}async loadGamesList(){const e=await L(async()=>{const{errno:a,stdout:n}=await u(`cat ${f.GAMES_FILE}`);return{errno:a,stdout:n}},"games-load");if(!e.ok){C("games-load",e.error),this.gamesList&&(this.gamesList.innerHTML='<li class="loading-text">加载失败</li>');return}const{errno:t,stdout:s}=e.data;if(t===0&&s.trim()){const a=this.parseTomlGames(s.trim());this.gamesListData=a,a.length>0?this.refreshGamesList():this.gamesList&&(this.gamesList.innerHTML=`<li class="loading-text">${o("config_games_not_found",{},this.currentLanguage)}</li>`)}else this.gamesList&&(this.gamesList.innerHTML=`<li class="loading-text">${o("config_games_list_not_found",{},this.currentLanguage)}</li>`)}parseTomlGames(e){const t=[],s=e.split(`
`);let a=null;for(const n of s){const i=n.trim();if(!(!i||i.startsWith("#"))){if(i.startsWith("[[games]]")){a&&t.push(a),a={};continue}if(a&&i.includes("=")){const[g,c]=i.split("="),d=g.trim(),h=c.trim().replace(/"/g,"");d==="package"?a.package=h:d==="mode"&&(a.mode=h)}}}return a&&t.push(a),t}refreshGamesList(){if(this.gamesList){if(this.gamesList.innerHTML="",this.gamesListData.length===0){this.gamesList.innerHTML=`<li class="loading-text">${o("config_games_not_found",{},this.currentLanguage)}</li>`;return}this.gamesListData.forEach((e,t)=>{const s=document.createElement("li"),a=document.createElement("span"),n=this.getModeText(e.mode||"balance");a.textContent=e.package?`${e.package} (${n})`:"",s.appendChild(a);const i=document.createElement("div");i.className="game-button-container";const g=document.createElement("button");g.className="game-edit-btn",g.innerHTML='<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3,17.25V21h3.75L17.81,9.94L14.06,6.19L3,17.25M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.13,5.12L18.88,8.87L20.71,7.04Z"/></svg>',g.title="编辑",g.onclick=d=>{d.stopPropagation(),this.editGameItem(t)},i.appendChild(g);const c=document.createElement("button");c.className="game-delete-btn",c.innerHTML='<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>',c.title="删除",c.onclick=d=>{d.stopPropagation(),this.deleteGameItem(t)},i.appendChild(c),s.appendChild(i),this.gamesList?.appendChild(s)})}}getModeText(e){const t={zh:{powersave:"省电",balance:"均衡",performance:"性能",fast:"极速"},en:{powersave:"Powersave",balance:"Balance",performance:"Performance",fast:"Fast"}};return t[this.currentLanguage][e]||t[this.currentLanguage].balance}openGameModal(){this.packageNameInput&&(this.packageNameInput.value=""),this.gameModeSelect&&(this.gameModeSelect.value="balance"),this.updateGameModeSelectDisplay("balance"),this.editGameModal&&(this.editGameModal.style.display="block"),this.editingIndex=-1}editGameItem(e){if(e<0||e>=this.gamesListData.length)return;const t=this.gamesListData[e];this.packageNameInput&&(this.packageNameInput.value=t.package||""),this.gameModeSelect&&(this.gameModeSelect.value=t.mode||"balance"),this.updateGameModeSelectDisplay(t.mode||"balance"),this.editGameModal&&(this.editGameModal.style.display="block"),this.editingIndex=e}closeGameModal(){this.editGameModal&&(this.editGameModal.style.display="none"),this.editingIndex=-1}saveGameItem(){if(!this.packageNameInput)return;const e=this.packageNameInput.value.trim(),t=this.gameModeSelect?this.gameModeSelect.value:"balance";if(!e){l(o("toast_game_invalid",{},this.currentLanguage));return}if(this.editingIndex===-1){if(this.gamesListData.some(s=>s.package===e)){l(o("toast_game_exists",{},this.currentLanguage));return}this.gamesListData.push({package:e,mode:t})}else this.gamesListData[this.editingIndex].package=e,this.gamesListData[this.editingIndex].mode=t;this.closeGameModal(),this.refreshGamesList(),this.editingIndex===-1&&l(o("toast_game_added",{},this.currentLanguage))}initGameModeSelect(){if(!this.gameModeContainer||!this.selectedGameMode||!this.gameModeOptions)return;this.selectedGameMode.addEventListener("click",t=>{t.stopPropagation(),this.gameModeContainer?.classList.toggle("open")});const e=this.gameModeOptions.querySelectorAll(".option");e.forEach(t=>{t.addEventListener("click",s=>{s.stopPropagation();const a=t.getAttribute("data-value"),n=t.textContent;this.selectedGameMode&&(this.selectedGameMode.textContent=n),this.gameModeSelect&&a&&(this.gameModeSelect.value=a,this.gameModeSelect.dispatchEvent(new Event("change"))),e.forEach(i=>{i.classList.remove("selected")}),t.classList.add("selected"),this.gameModeContainer?.classList.remove("open")})}),document.addEventListener("click",t=>{this.gameModeContainer?.contains(t.target)||this.gameModeContainer?.classList.remove("open")})}updateGameModeSelectDisplay(e){if(!this.selectedGameMode||!this.gameModeOptions)return;const t=this.gameModeOptions.querySelector(`.option[data-value="${e}"]`);t&&(this.selectedGameMode.textContent=t.textContent,this.gameModeOptions.querySelectorAll(".option").forEach(a=>{a.classList.remove("selected")}),t.classList.add("selected"))}deleteGameItem(e){e>=0&&e<this.gamesListData.length?(this.gamesListData.splice(e,1),this.refreshGamesList(),l(o("toast_game_deleted",{},this.currentLanguage))):l(o("toast_index_invalid",{},this.currentLanguage))}async saveGamesToFile(){if(this.gamesListData.length===0){l(o("toast_games_empty",{},this.currentLanguage));return}let e=`# GPU调速器游戏列表配置文件

`;this.gamesListData.forEach(a=>{e+=`[[games]]
`,e+=`package = "${a.package}"
`,e+=`mode = "${a.mode||"balance"}"

`});const t=btoa(unescape(encodeURIComponent(e))),s=await L(async()=>await u(`echo '${t}' | base64 -d > ${f.GAMES_FILE}`),"games-save");if(!s.ok){l(o("toast_games_save_fail",{},this.currentLanguage));return}s.data.errno===0?l(o("toast_games_saved",{},this.currentLanguage)):l(o("toast_games_save_fail",{},this.currentLanguage))}setLanguage(e){if(this.currentLanguage=e,this.gameModeSelect){const t=this.gameModeSelect.options;for(let s=0;s<t.length;s++){const a=t[s],i=`status_mode_${a.value}`;m[e]?.[i]&&(a.textContent=m[e][i])}}if(this.gameModeOptions&&this.gameModeOptions.querySelectorAll(".option").forEach(s=>{const n=`status_mode_${s.getAttribute("data-value")}`;m[e]?.[n]&&(s.textContent=m[e][n])}),this.selectedGameMode&&this.gameModeSelect){const s=`status_mode_${this.gameModeSelect.value}`;m[e]?.[s]&&(this.selectedGameMode.textContent=m[e][s])}this.refreshGamesList()}}class q{currentLanguage="zh";logContent;refreshLogBtn;constructor(){this.logContent=document.getElementById("logContent"),this.refreshLogBtn=document.getElementById("refreshLogBtn")}init(){this.setupEventListeners(),this.initLogFileSelect()}setupEventListeners(){this.refreshLogBtn&&this.refreshLogBtn.addEventListener("click",()=>this.loadLog());const e=document.querySelectorAll(".log-tab-btn");e.forEach(t=>{t.addEventListener("click",()=>{t.classList.contains("active")||(e.forEach(s=>{s.classList.remove("active")}),t.classList.add("active"),this.logContent&&(this.logContent.style.opacity="0.5",this.logContent.textContent=o("log_loading",{},this.currentLanguage)),setTimeout(()=>{this.loadLog().then(()=>{this.logContent&&(this.logContent.style.opacity="1")})},100))})})}initLogFileSelect(){const e=document.querySelector(".log-tab-btn.active"),t=e?e.getAttribute("data-log"):"gpu_gov.log";document.querySelectorAll(".log-tab-btn").forEach(a=>{a.getAttribute("data-log")===t?a.classList.add("active"):a.classList.remove("active")})}async loadLog(){try{const e=document.querySelector(".log-tab-btn.active"),t=e?e.getAttribute("data-log"):"gpu_gov.log";this.logContent&&(this.logContent.textContent=o("log_loading",{},this.currentLanguage));const{errno:s,stdout:a}=await u(`cat ${f.LOG_PATH}/${t} 2>/dev/null || echo "日志文件不存在"`);if(a.trim()==="日志文件不存在"){this.logContent&&(this.logContent.textContent=o("log_not_found",{},this.currentLanguage));return}if(s===0){const i=a.trim().split(`
`).slice(-100).join(`
`);this.logContent&&(this.logContent.textContent=i||o("log_empty",{},this.currentLanguage)),this.logContent&&(this.logContent.scrollTop=this.logContent.scrollHeight)}else this.logContent&&(this.logContent.textContent=o("log_not_found",{},this.currentLanguage))}catch(e){console.error("加载日志失败:",e),this.logContent&&(this.logContent.textContent="加载日志失败，请检查权限")}}setLanguage(e){this.currentLanguage=e}}class A{currentLanguage="zh";logLevelContainer;languageContainer;constructor(){this.logLevelContainer=document.getElementById("logLevelContainer"),this.languageContainer=document.getElementById("languageContainer")}init(){this.setupEventListeners(),this.loadLogLevel()}setupEventListeners(){if(this.logLevelContainer){const e=this.logLevelContainer.querySelectorAll(".settings-tab-btn");e.forEach(t=>{t.addEventListener("click",s=>{s.preventDefault(),e.forEach(a=>{a.classList.remove("active")}),t.classList.add("active"),this.saveLogLevel()})})}if(this.languageContainer){const e=this.languageContainer.querySelectorAll(".settings-tab-btn");e.forEach(t=>{t.addEventListener("click",async s=>{s.preventDefault(),e.forEach(g=>{g.classList.remove("active")}),t.classList.add("active");const a=t.getAttribute("data-value");localStorage.setItem("languageSetting",a);let n="zh";if(a==="system"){try{const{errno:g,stdout:c}=await u('getprop persist.sys.locale || getprop ro.product.locale || echo "zh-CN"');g===0&&c.trim()&&(n=c.trim().toLowerCase().startsWith("en")?"en":"zh")}catch{n="zh"}localStorage.setItem("language",n),l(o("toast_language_follow_system",{},n))}else n=a,localStorage.setItem("language",a),l(o("toast_language_changed",{language:a==="zh"?"中文":"English"},n));this.currentLanguage=n;const i=new CustomEvent("languageChange",{detail:{language:n}});document.dispatchEvent(i)})})}}async loadLogLevel(){try{const{errno:e,stdout:t}=await u(`cat ${f.LOG_LEVEL_PATH} 2>/dev/null || echo "info"`);let s="info";if(e===0){const a=t.trim().toLowerCase();["debug","info","warn","error"].includes(a)&&(s=a)}this.logLevelContainer&&this.logLevelContainer.querySelectorAll(".settings-tab-btn").forEach(n=>{n.getAttribute("data-value")===s?n.classList.add("active"):n.classList.remove("active")})}catch(e){if(console.error("加载日志等级设置失败:",e),this.logLevelContainer){const t=this.logLevelContainer.querySelector('.settings-tab-btn[data-value="info"]');t&&(this.logLevelContainer.querySelectorAll(".settings-tab-btn").forEach(a=>{a.classList.remove("active")}),t.classList.add("active"))}}}async saveLogLevel(){try{if(!this.logLevelContainer)return;const e=this.logLevelContainer.querySelector(".settings-tab-btn.active");if(!e)return;const t=e.getAttribute("data-value"),{errno:s}=await u(`echo "${t}" > ${f.LOG_LEVEL_PATH}`);l(s===0?t==="debug"?o("toast_log_level_debug",{},this.currentLanguage):o("toast_log_level_set",{level:t||""},this.currentLanguage):o("toast_log_level_fail",{},this.currentLanguage))}catch(e){console.error("保存日志等级失败:",e);const t=e instanceof Error?e.message:String(e);l(`保存日志等级失败: ${t}`)}}setLanguage(e){this.currentLanguage=e}}class D{themeToggle;followSystemThemeToggle;followSystemThemeSuperSwitch;currentLanguage="zh";constructor(){this.themeToggle=document.getElementById("themeToggle"),this.followSystemThemeToggle=document.querySelector("#followSystemThemeToggle .miuix-switch-input"),this.followSystemThemeSuperSwitch=document.getElementById("followSystemThemeSuperSwitch")}init(){this.initTheme(),this.setupEventListeners()}initTheme(){const e=localStorage.getItem("theme"),t=window.matchMedia?.("(prefers-color-scheme: dark)").matches,s=localStorage.getItem("followSystemTheme"),a=s===null?!0:s==="true";this.followSystemThemeToggle&&(this.followSystemThemeToggle.checked=a),s===null&&localStorage.setItem("followSystemTheme","true");let n;if(a?n=t?"dark":"light":e?n=e:n=t?"dark":"light",document.documentElement.setAttribute("data-theme",n),localStorage.setItem("theme",n),window.matchMedia){const i=window.matchMedia("(prefers-color-scheme: dark)");i.addEventListener&&i.addEventListener("change",g=>{if(localStorage.getItem("followSystemTheme")==="true"){const c=g.matches?"dark":"light";document.documentElement.setAttribute("data-theme",c),localStorage.setItem("theme",c)}})}}setupEventListeners(){this.themeToggle&&this.themeToggle.addEventListener("click",()=>this.toggleTheme()),this.followSystemThemeSuperSwitch&&(this.followSystemThemeSuperSwitch.addEventListener("click",e=>this.handleFollowSystemToggleClick(e)),this.followSystemThemeSuperSwitch.addEventListener("keydown",e=>{(e.key==="Enter"||e.key===" ")&&(e.preventDefault(),e.currentTarget.click())}),this.followSystemThemeSuperSwitch.setAttribute("tabindex","0"),this.followSystemThemeSuperSwitch.setAttribute("role","switch"),this.followSystemThemeSuperSwitch.setAttribute("aria-checked",this.followSystemThemeToggle?.checked?"true":"false")),this.followSystemThemeToggle&&this.followSystemThemeToggle.addEventListener("change",()=>this.handleFollowSystemChange())}toggleTheme(){this.themeToggle&&this.themeToggle.classList.add("switching"),localStorage.getItem("followSystemTheme")==="true"&&(localStorage.setItem("followSystemTheme","false"),this.followSystemThemeToggle&&(this.followSystemThemeToggle.checked=!1),l(o("toast_theme_follow_disabled",{},this.currentLanguage)));const t=document.documentElement.getAttribute("data-theme")==="light"?"dark":"light";document.documentElement.style.transition="background-color 0.3s ease, color 0.3s ease",document.documentElement.setAttribute("data-theme",t),localStorage.setItem("theme",t),setTimeout(()=>{this.themeToggle&&this.themeToggle.classList.remove("switching"),document.documentElement.style.transition=""},600),l(o(t==="dark"?"toast_theme_switched_dark":"toast_theme_switched_light",{},this.currentLanguage))}handleFollowSystemToggleClick(e){e.target!==this.followSystemThemeToggle&&(e.preventDefault(),navigator.vibrate&&navigator.vibrate(50),this.followSystemThemeToggle&&(this.followSystemThemeToggle.checked=!this.followSystemThemeToggle.checked,this.followSystemThemeToggle.dispatchEvent(new Event("change",{bubbles:!0}))))}handleFollowSystemChange(){const e=!!this.followSystemThemeToggle?.checked;if(localStorage.setItem("followSystemTheme",e.toString()),this.followSystemThemeSuperSwitch&&this.followSystemThemeSuperSwitch.setAttribute("aria-checked",e?"true":"false"),this.followSystemThemeSuperSwitch&&(this.followSystemThemeSuperSwitch.style.transform="scale(0.98)",setTimeout(()=>{this.followSystemThemeSuperSwitch&&(this.followSystemThemeSuperSwitch.style.transform="")},150)),e){const t=window.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.setAttribute("data-theme",t),localStorage.setItem("theme",t),l(o("toast_theme_follow_enabled",{},this.currentLanguage))}else l(o("toast_theme_follow_keep",{},this.currentLanguage))}setLanguage(e){this.currentLanguage=e}}class N{currentLanguage="zh";currentPage="page-status";app;loading;htmlRoot;moduleVersion;currentMode;runningStatus;pages;navItems;languageContainer;themeManager;configManager;gamesManager;logManager;settingsManager;constructor(){this.app=document.getElementById("app"),this.loading=document.getElementById("loading"),this.htmlRoot=document.getElementById("htmlRoot"),this.moduleVersion=document.getElementById("moduleVersion"),this.currentMode=document.getElementById("currentMode"),this.runningStatus=document.getElementById("runningStatus"),this.pages=document.querySelectorAll(".page"),this.navItems=document.querySelectorAll(".nav-item"),this.languageContainer=document.getElementById("languageContainer"),this.themeManager=new D,this.configManager=new x,this.gamesManager=new V,this.logManager=new q,this.settingsManager=new A}async init(){try{this.loading&&(this.loading.style.display="none"),this.app&&(this.app.style.display="block"),this.themeManager.init(),this.configManager.init(),this.gamesManager.init(),this.logManager.init(),this.settingsManager.init(),await this.initLanguage(),this.setupLanguageEvents(),this.setupNavigationEvents(),await this.loadData(),setInterval(()=>{this.loadCurrentMode(),this.checkModuleStatus()},2e3),l(o("toast_webui_loaded",{},this.currentLanguage))}catch(e){console.error("初始化失败:",e),this.loading&&(this.loading.style.display="none"),this.app&&(this.app.style.display="block")}}setupNavigationEvents(){this.navItems.forEach(e=>{e.addEventListener("click",()=>{const t=e.getAttribute("data-page");t&&this.switchPage(t)})})}setupLanguageEvents(){document.addEventListener("languageChange",e=>{const t=e,{language:s}=t.detail;this.currentLanguage=s,this.themeManager.setLanguage(this.currentLanguage),this.configManager.setLanguage(this.currentLanguage),this.gamesManager.setLanguage(this.currentLanguage),this.logManager.setLanguage(this.currentLanguage),this.settingsManager.setLanguage(this.currentLanguage),this.applyTranslations(),this.loadModuleVersion(),this.loadCurrentMode(),this.checkModuleStatus()})}async initLanguage(){const e=localStorage.getItem("languageSetting"),t=localStorage.getItem("language");e===null&&localStorage.setItem("languageSetting","system"),e==="system"||e===null?(this.currentLanguage=await this.detectSystemLanguage(),localStorage.setItem("language",this.currentLanguage)):t&&(this.currentLanguage=t),this.themeManager.setLanguage(this.currentLanguage),this.configManager.setLanguage(this.currentLanguage),this.gamesManager.setLanguage(this.currentLanguage),this.logManager.setLanguage(this.currentLanguage),this.settingsManager.setLanguage(this.currentLanguage),this.applyTranslations(),this.updateSelectedLanguageText(e||"system"),this.setupLanguageEvents()}async detectSystemLanguage(){try{const e=navigator,t=navigator.language||e.userLanguage||"zh-CN";try{const{errno:s,stdout:a}=await u('getprop persist.sys.locale || getprop ro.product.locale || echo "zh-CN"');if(s===0&&a.trim())return a.trim().toLowerCase().startsWith("en")?"en":"zh"}catch{console.log("无法通过系统属性检测语言，将使用浏览器语言")}return t.startsWith("en")?"en":"zh"}catch(e){return console.error("检测系统语言失败:",e),"zh"}}applyTranslations(){try{document.title=o("title",{},this.currentLanguage),this.htmlRoot&&this.htmlRoot.setAttribute("lang",this.currentLanguage==="en"?"en":"zh-CN"),this.loading&&(this.loading.textContent=o("loading",{},this.currentLanguage));const e=document.querySelector(".header-content h1");e&&(e.textContent=o("header_title",{},this.currentLanguage)),document.querySelectorAll(".nav-item").forEach(t=>{const s=t.getAttribute("data-page"),a=t.querySelector(".nav-text");a&&(s==="page-status"?a.textContent=o("nav_status",{},this.currentLanguage):s==="page-config"?a.textContent=o("nav_config",{},this.currentLanguage):s==="page-log"?a.textContent=o("nav_log",{},this.currentLanguage):s==="page-settings"&&(a.textContent=o("nav_settings",{},this.currentLanguage)))})}catch(e){console.error("应用基本翻译失败:",e)}try{document.querySelectorAll("[data-i18n]").forEach(e=>{const t=e.getAttribute("data-i18n");t&&m[this.currentLanguage]&&m[this.currentLanguage][t]&&(e.textContent=o(t,{},this.currentLanguage))})}catch{console.error("批量应用 data-i18n 国际化失败")}try{document.querySelectorAll("[data-i18n-placeholder]").forEach(e=>{const t=e.getAttribute("data-i18n-placeholder");t&&m[this.currentLanguage]&&m[this.currentLanguage][t]&&(e.placeholder=o(t,{},this.currentLanguage))})}catch{console.error("批量应用 data-i18n-placeholder 国际化失败")}}updateSelectedLanguageText(e){try{if(!this.languageContainer)return;this.languageContainer.querySelectorAll(".settings-tab-btn").forEach(a=>{a.classList.remove("active")});const s=this.languageContainer.querySelector(`.settings-tab-btn[data-value="${e}"]`);if(s)s.classList.add("active");else{const a=this.languageContainer.querySelector('.settings-tab-btn[data-value="system"]');a&&a.classList.add("active")}}catch(t){console.error("更新语言按钮状态失败:",t)}}async loadData(){const e=[{fn:()=>this.checkModuleStatus(),context:"check-module-status",failMsg:"检查模块状态失败"},{fn:()=>this.loadModuleVersion(),context:"load-module-version",failMsg:"加载模块版本失败"},{fn:()=>this.loadCurrentMode(),context:"load-current-mode",failMsg:"加载当前模式失败"},{fn:()=>this.configManager.loadGpuConfig(),context:"load-gpu-config",failMsg:"加载GPU配置失败"},{fn:()=>this.gamesManager.loadGamesList(),context:"load-games-list",failMsg:"加载游戏列表失败"},{fn:()=>this.logManager.loadLog(),context:"load-log",failMsg:"加载日志失败"},{fn:()=>this.settingsManager.loadLogLevel(),context:"load-log-level",failMsg:"加载日志等级设置失败"}];for(const t of e){const s=await L(t.fn,t.context);s.ok||(C(t.context,s.error),console.error(t.failMsg,s.error))}this.switchPage("page-status")}switchPage(e){this.pages.forEach(s=>{s.classList.remove("active")});const t=document.getElementById(e);t&&t.classList.add("active"),this.navItems.forEach(s=>{s.getAttribute("data-page")===e?s.classList.add("active"):s.classList.remove("active")}),this.currentPage=e}async checkModuleStatus(){try{const{errno:e,stdout:t}=await u('pgrep -f gpugovernor || echo ""'),s=e===0&&t.trim(),a=this.runningStatus?.classList.contains("status-running");s!==a&&this.runningStatus&&(this.runningStatus.classList.add("status-changing"),this.runningStatus.removeAttribute("data-i18n"),setTimeout(()=>{s&&this.runningStatus?(this.runningStatus.textContent=o("status_running_active",{},this.currentLanguage),this.runningStatus.className="status-badge status-running"):this.runningStatus&&(this.runningStatus.textContent=o("status_running_inactive",{},this.currentLanguage),this.runningStatus.className="status-badge status-stopped"),setTimeout(()=>{this.runningStatus?.classList.remove("status-changing")},600)},100))}catch(e){console.error("检查模块状态失败:",e),this.runningStatus&&(this.runningStatus.textContent=o("status_checking",{},this.currentLanguage),this.runningStatus.className="status-badge status-stopped",this.runningStatus.removeAttribute("data-i18n"))}}async loadModuleVersion(){try{const{errno:e,stdout:t}=await u('grep -i "^version=" /data/adb/modules/Mediatek_Mali_GPU_Governor/module.prop | cut -d= -f2');if(e===0&&t.trim())this.moduleVersion&&(this.moduleVersion.textContent=t.trim(),this.moduleVersion.removeAttribute("data-i18n"));else{const{errno:s,stdout:a}=await u('grep -i "^version=" /data/adb/ksu/modules/Mediatek_Mali_GPU_Governor/module.prop | cut -d= -f2');s===0&&a.trim()?this.moduleVersion&&(this.moduleVersion.textContent=a.trim(),this.moduleVersion.removeAttribute("data-i18n")):this.moduleVersion&&(this.moduleVersion.textContent=this.currentLanguage==="en"?"Unknown":"未知",this.moduleVersion.removeAttribute("data-i18n"))}}catch(e){console.error("加载模块版本失败:",e),this.moduleVersion&&(this.moduleVersion.textContent=this.currentLanguage==="en"?"Unknown":"未知",this.moduleVersion.removeAttribute("data-i18n"))}}async loadCurrentMode(){try{const{errno:e,stdout:t}=await u(`cat ${f.CURRENT_MODE_PATH} 2>/dev/null || echo "unknown"`);let s="unknown";if(e===0&&(s=t.trim().toLowerCase()),["powersave","balance","performance","fast"].includes(s)||(s="unknown"),this.currentMode){const n={powersave:o("status_mode_powersave",{},this.currentLanguage),balance:o("status_mode_balance",{},this.currentLanguage),performance:o("status_mode_performance",{},this.currentLanguage),fast:o("status_mode_fast",{},this.currentLanguage),unknown:o("status_mode_unknown",{},this.currentLanguage)};this.currentMode.textContent=n[s]||n.unknown,this.currentMode.className=`mode-badge ${s}`,this.currentMode.removeAttribute("data-i18n")}}catch(e){console.error("加载当前模式失败:",e),this.currentMode&&(this.currentMode.textContent=o("status_mode_unknown",{},this.currentLanguage),this.currentMode.className="mode-badge default",this.currentMode.removeAttribute("data-i18n"))}}}document.addEventListener("DOMContentLoaded",async()=>{console.debug("[main] 生产模式：跳过组件动态加载（已内联）"),await new N().init()});
