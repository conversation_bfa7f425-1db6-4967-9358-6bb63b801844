# 使用指南

## WebUI 界面

本模块提供了基于 KernelSU API 的现代化 WebUI 界面，基于 Miuix 风格设计，为用户提供直观的 GPU 调速器管理和监控体验。
WebUI 支持配置文件热更新、日志实时查看、游戏列表和频率表可视化编辑，所有更改即时生效。

### 功能特性

#### WebUI 功能

- **实时状态监控**：查看运行状态、当前模式和版本信息
- **GPU 频率配置**：查看和编辑当前 GPU 频率表配置，支持调整频率、电压和内存档位
- **自定义配置**：查看和编辑当前自定义配置，支持全局配置和模式配置
- **游戏列表管理**：查看和编辑已配置的游戏列表，支持添加/删除游戏和选择对应模式
- **日志查看**：实时查看模块运行日志，支持选择不同日志文件和日志等级

#### 界面特性

- **深色模式支持**：自动适应系统深色/浅色模式，也可手动切换
- **多语言支持**：支持中文和英文界面，自动检测系统语言设置
- **电压调整器**：支持使用旋转选择器进行电压调整，长按可连续调整（每次±625单位）
- **实时更新**：每秒检测游戏模式状态变化并更新界面
- **Toast 提示**：操作反馈和状态提示

### 界面布局

WebUI 采用多页面布局，通过底部导航栏进行页面切换。

### 如何访问 WebUI

- **KernelSU/APatch 用户**：在root管理器中点击本模块，选择"打开 WebUI"
- **Magisk 用户**：需要先安装 [KsuWebUI](https://github.com/5ec1cff/KsuWebUIStandalone) 或 [SSU](https://ssu.oom-wg.dev/base/install) 应用

## 交互式控制菜单

模块提供了 `action.sh` 脚本，支持通过音量键进行交互式操作：

### 脚本功能

- **控制调速器服务**：启动或停止 GPU 调速器服务
- **设置日志等级**：选择 debug、info、warn 或 error 级别
- **查看模块状态**：显示模块版本、运行状态等信息

### 操作方式

- 音量上键：向下选择选项（在菜单中递增选择项）
- 音量下键：确认选择

脚本会自动检测当前系统语言，并显示相应的中文或英文界面。

### 模块文件

- 日志等级设置：`/data/adb/gpu_governor/log/log_level`
- 游戏列表配置：`/data/adb/gpu_governor/game/games.toml`
- 进程ID管理：`/data/adb/gpu_governor/gpu_governor.pid`

## 日志系统

日志文件存储在 `/data/adb/gpu_governor/log/` 目录下，主要包括：

- **gpu_gov.log**: 主日志文件，由 Rust 核心统一管理，记录 GPU 调速器的运行状态
- **initsvc.log**: 初始化日志，记录模块启动过程和脚本初始化信息

日志内容可以通过WebUI界面查看，也可以通过文件管理器直接查看。

### 日志管理

模块的主日志已完全由Rust核心统一实现，包括：

- 日志文件的创建和写入
- 自动日志轮转和大小控制
- 日志等级的实时监控和响应

## 游戏模式

### 自动游戏检测

模块会自动检测 `games.toml` 中配置的游戏应用，当检测到游戏在前台运行时，会自动应用游戏模式的优化策略。

### 游戏列表管理

- 安装时模块会自动扫描设备上已安装的游戏并生成初始游戏列表
- 可以通过 WebUI 界面添加/删除游戏
- 可以为每个游戏指定不同的性能模式
- 游戏列表支持热更新，修改后立即生效

### 性能优化

游戏模式下可以应用以下优化：

- 使用激进升频策略
- 启用游戏特殊内存优化
- 调整采样间隔以获得更好的响应性
- 根据游戏需求应用对应的性能模式