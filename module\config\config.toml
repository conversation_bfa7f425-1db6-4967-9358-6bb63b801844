# GPU调速器配置文件

[global]
# 全局模式设置: powersave, balance, performance, fast
mode = "balance"
# 空闲阈值（百分比）
idle_threshold = 5

# 省电模式 - 更高的升频阈值，更激进的降频
[powersave]
# 余量
margin = 0
# 是否使用激进降频策略
aggressive_down = true
# 采样间隔（毫秒）
sampling_interval = 16
# 游戏优化 - 启用游戏特殊内存优化
gaming_mode = false
# 自适应采样
adaptive_sampling = true
# 自适应采样最小间隔（毫秒）
min_adaptive_interval = 16
# 自适应采样最大间隔（毫秒）
max_adaptive_interval = 33
# 升频延迟（毫秒）
up_rate_delay = 270
# 降频延迟（毫秒）
down_rate_delay = 0

# 平衡模式 - 默认设置
[balance]
margin = 0
aggressive_down = true
sampling_interval = 8
gaming_mode = false
adaptive_sampling = true
min_adaptive_interval = 8
max_adaptive_interval = 33
up_rate_delay = 100
down_rate_delay = 0

# 性能模式 - 更低的升频阈值，更保守的降频
[performance]
margin = 35
aggressive_down = false
sampling_interval = 8
gaming_mode = true
adaptive_sampling = true
min_adaptive_interval = 8
max_adaptive_interval = 16
up_rate_delay = 50
down_rate_delay = 50

# 极速模式 - 最低的升频阈值，最保守的降频
[fast]
margin = 45
aggressive_down = false
sampling_interval = 8
gaming_mode = true
adaptive_sampling = false
min_adaptive_interval = 1
max_adaptive_interval = 8
up_rate_delay = 20
down_rate_delay = 500