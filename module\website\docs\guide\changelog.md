---
layout: doc
---

# 📝 更新日志

## 🚀 v2.8.0 → v2.9.0 (2025-08-03)

> ⚠️ **重要提醒**
>
> 由于配置文件变更极大，建议备份旧配置文件卸载模块重启后安装。

### ✨ 新增特性

- **新增自定义配置功能** ⚙️
  - 用户现在可以自定义配置模块行为，提供更多个性化选项。自定义配置具体详情可查看模块docs文件夹中文档。
- **新增众多可调配置项** 🛠️
  - 增加了大量可配置参数，让用户能够更精细地控制模块功能。
- **将余量（Margin）配置项从频率表分离到自定义配置** 📊
  - 余量配置现在独立于频率表，用户可以单独调整，提升配置灵活性。

### 🔧 功能改进

- **优化欢迎信息** 👋
  - 改进了模块安装和启动时的欢迎信息显示效果。
- **优化启动时日志轮转** 📒
  - 提升了启动时日志轮转的效率和稳定性。
- **重构日志轮转功能** 🔄
  - 对日志轮转机制进行了重构，提高代码质量和维护性。
- **重构游戏检测** 🎮
  - 重新设计了游戏检测逻辑，提高准确性。
- **移除游戏模式文件，加入当前模式文件** 📄
  - 简化了模式管理，移除了游戏模式文件，新增当前模式文件来统一管理。
- **优化模块脚本** 🧠
  - 对模块脚本进行了优化，提升执行效率和稳定性。
- **重构游戏列表** 🕹️
  - 重新设计了游戏列表管理机制，提高维护性和扩展性。
- **重构频率表** 📈
  - 对频率表结构进行了重构，优化了数据管理和访问效率。
- **重构WebUI模块化架构** 🌐
  - WebUI部分进行了模块化重构，提升代码结构和可维护性。

### 🪲 问题修复

- **修复了一些已知问题** 🛠️
  - 解决了在特定场景下可能出现的异常情况。

### 🗑️ 移除/调整

- **调整配置文件结构** 📁
  - 重新组织了配置文件的结构，使其更加清晰和易于管理。