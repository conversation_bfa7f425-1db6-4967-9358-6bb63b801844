import { defineConfig } from 'vitepress'
import locales from './locales'
import * as path from 'path'
import * as fs from 'fs'
import { fileURLToPath } from 'url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

export default defineConfig( {
    title: 'Mediatek Mali GPU Governor',
    base: '/Mediatek_Mali_GPU_Governor/',
    locales: locales.locales,
    head: [
        ['link', { rel: 'icon', href: '/logo.png' }],
        ['meta', { name: 'theme-color', content: '#0d84ff' }],
        ['style', {}, `
        :root {
          --vp-c-brand-1: #0d84ff;
          --vp-c-brand-2: #0d84ff;
          --vp-c-brand-3: #0d84ff;
          --vp-c-brand-soft: rgba(13, 132, 255, 0.14);
          --vp-c-text-accent-1: #0d84ff;
          --vp-button-brand-bg: #0d84ff;
          --vp-button-brand-hover-bg: #0a6edb;
        }
        .dark {
          --vp-c-brand-1: #0d84ff;
          --vp-c-brand-2: #0d84ff;
          --vp-c-brand-3: #0d84ff;
          --vp-c-brand-soft: rgba(13, 132, 255, 0.16);
          --vp-c-text-accent-1: #0d84ff;
          --vp-button-brand-bg: #0d84ff;
          --vp-button-brand-hover-bg: #3a9bff;
        }
        `],
    ],
    sitemap: {
        hostname: 'https://seyud.github.io/Mediatek_Mali_GPU_Governor'
    },
    markdown: {
        config: (md) => {
            // 自定义处理 @@include 语法
            md.use((md) => {
                const defaultRender = md.render
                md.render = function(src, env) {
                    // 处理 @@include 语法
                    src = src.replace(/@@include\(([^)]+)\)/g, (match, includePath) => {
                        try {
                            // __dirname 是 .vitepress 目录，所以 docs 目录是上一级
                            const docsDir = path.dirname(__dirname)
                            
                            // 处理相对路径，移除开头的 ../
                            const relativePath = includePath.replace(/^\.\.\//, '')
                            const fullPath = path.join(docsDir, relativePath)
                            
                            return fs.readFileSync(fullPath, 'utf-8')
                        } catch (e) {
                            console.warn(`Failed to include file: ${includePath}, error: ${e}`)
                            return match
                        }
                    })
                    return defaultRender.call(this, src, env)
                }
            })
        }
    }
})
