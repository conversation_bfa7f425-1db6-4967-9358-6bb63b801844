@keyframes ellipsis{0%,to{content:"."}33%{content:".."}66%{content:"..."}}@keyframes sunPulse{0%,to{opacity:.9;transform:scale(1)}50%{opacity:1;transform:scale(1.1)}}@keyframes sunRays{0%{transform:rotate(0deg);opacity:.8}50%{opacity:1}to{transform:rotate(360deg);opacity:.8}}@keyframes moonGlow{0%,to{opacity:.9;filter:brightness(1)}50%{opacity:1;filter:brightness(1.1)}}@keyframes crater{0%,to{opacity:.3;transform:scale(1)}50%{opacity:.5;transform:scale(1.05)}}@keyframes twinkle{0%,to{opacity:.5;transform:scale(1)}50%{opacity:.8;transform:scale(1.2)}}@keyframes themeSwitch{0%{transform:translate(-50%,-50%) scale(0);opacity:.8}50%{transform:translate(-50%,-50%) scale(1.5);opacity:.4}to{transform:translate(-50%,-50%) scale(2);opacity:0}}@keyframes cardFloatIn{0%{opacity:0;transform:translateY(24px) scale(.98);filter:blur(6px)}60%{opacity:1;transform:translateY(-4px) scale(1.01);filter:blur(0)}to{opacity:1;transform:translateY(0) scale(1);filter:blur(0)}}@keyframes cardGlow{0%,to{box-shadow:0 0 0 rgba(var(--accent-color-rgb, 13, 132, 255),0)}50%{box-shadow:0 0 32px rgba(var(--accent-color-rgb, 13, 132, 255),.22)}}@keyframes cardFadeOut{0%{opacity:1;transform:translateY(0) scale(1);filter:blur(0)}to{opacity:0;transform:translateY(8px) scale(.97);filter:blur(4px)}}@keyframes toastSlideIn{0%{transform:translateY(12px) scale(.96);opacity:0}60%{transform:translateY(-3px) scale(1.02);opacity:1}to{transform:translateY(0) scale(1);opacity:1}}@keyframes modalOverlayIn{0%{opacity:0}to{opacity:1}}@keyframes modalContentIn{0%{transform:translateY(24px) scale(.95);opacity:0}60%{transform:translateY(-6px) scale(1.01);opacity:1}to{transform:translateY(0) scale(1);opacity:1}}@keyframes statusPulse{0%{transform:translate(-50%,-50%) scale(1);opacity:.6}70%,to{transform:translate(-50%,-50%) scale(2.4);opacity:0}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}@keyframes slideIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}@keyframes modalFadeIn{0%{opacity:0}to{opacity:1}}@keyframes modalSlideIn{0%{opacity:0;transform:translateY(-20px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}:root{--background-color:#f5f5f5;--card-background:#ffffff;--text-color:#333333;--secondary-text-color:#666666;--border-color:#e0e0e0;--accent-color:#0d84ff;--accent-color-rgb:13, 132, 255;--accent-hover:#0a6edb;--accent-bg:rgba(13, 132, 255, 0.1);--success-color:#4caf50;--warning-color:#ff9800;--error-color:#f44336;--header-background:#ffffff;--header-shadow:0 2px 8px rgba(0, 0, 0, 0.1);--card-shadow:0 2px 4px rgba(0, 0, 0, 0.05);--switch-background:#e0e0e0}[data-theme=dark]{--background-color:#121212;--card-background:#1e1e1e;--text-color:#e0e0e0;--secondary-text-color:#aaaaaa;--border-color:#333333;--accent-color:#0d84ff;--accent-color-rgb:13, 132, 255;--accent-hover:#3a9bff;--accent-bg:rgba(13, 132, 255, 0.2);--success-color:#66bb6a;--warning-color:#ffa726;--error-color:#ef5350;--header-background:#1e1e1e;--header-shadow:0 2px 8px rgba(0, 0, 0, 0.2);--card-shadow:0 2px 4px rgba(0, 0, 0, 0.2);--switch-background:#333333}*{margin:0;padding:0;box-sizing:border-box;transition:background-color .3s ease,color .3s ease,border-color .3s ease,box-shadow .3s ease}:focus{outline:0}.card:focus,button:focus{box-shadow:0 0 0 2px rgba(13,132,255,.3)}.theme-toggle:focus{box-shadow:0 0 0 2px rgba(13,132,255,.4);background-color:var(--accent-bg)}.nav-item:focus{box-shadow:none}body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Open Sans","Helvetica Neue",sans-serif;background-color:var(--background-color);color:var(--text-color);line-height:1.6;transition:background-color .3s,color .3s;padding-bottom:90px}#loading{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);font-size:18px;font-weight:500}#loading::after{content:".";animation:ellipsis 1.5s infinite}.app-header{background-color:var(--header-background);box-shadow:var(--header-shadow);padding:16px 20px;border-bottom:1px solid var(--border-color);backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px)}.header-content{max-width:800px;margin:0 auto;display:flex;justify-content:space-between;align-items:center}.header-content h1{font-size:20px;font-weight:600;color:var(--accent-color);background:linear-gradient(135deg,var(--accent-color) 0%,var(--accent-hover) 100%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;text-shadow:0 1px 2px rgba(13,132,255,.1)}[data-theme=dark] .app-header{background:linear-gradient(135deg,var(--header-background) 0%,rgba(13,132,255,.02) 100%);border-bottom-color:rgba(13,132,255,.2)}[data-theme=dark] .header-content h1{text-shadow:0 2px 4px rgba(13,132,255,.2)}.theme-toggle{cursor:pointer;padding:10px;border-radius:50%;transition:all .3s cubic-bezier(.4,0,.2,1);overflow:hidden}.theme-toggle:active,.theme-toggle:hover{background-color:var(--accent-bg)}.theme-toggle,.theme-toggle:active{outline:0;-webkit-tap-highlight-color:transparent}.theme-toggle svg{filter:drop-shadow(0 1px 2px rgba(0,0,0,.1))}.theme-toggle:hover svg{filter:drop-shadow(0 2px 4px rgba(13,132,255,.3))}.light-icon svg circle,.light-icon svg path{animation:sunPulse 3s ease-in-out infinite;transform-origin:center}.light-icon svg path{animation:sunRays 4s linear infinite}.theme-toggle:hover .light-icon svg circle{animation-duration:1.5s}.theme-toggle:hover .light-icon svg path{animation-duration:2s}.dark-icon svg{position:relative}.dark-icon svg path:first-child{animation:moonGlow 4s ease-in-out infinite}.dark-icon svg circle{animation:crater 6s ease-in-out infinite}.dark-icon svg path:not(:first-child){animation:twinkle 2s ease-in-out infinite}.theme-toggle:hover .dark-icon svg path:first-child{animation-duration:2s}.theme-toggle:hover .dark-icon svg path:not(:first-child){animation-duration:1s}.light-icon{display:inline-block}.dark-icon,[data-theme=dark] .light-icon{display:none}[data-theme=dark] .dark-icon{display:inline-block}[data-theme=dark] .theme-toggle{background:radial-gradient(circle at center,rgba(13,132,255,.1)0,transparent 70%)}[data-theme=dark] .theme-toggle:hover{background:radial-gradient(circle at center,rgba(13,132,255,.2)0,transparent 70%);box-shadow:0 0 12px rgba(13,132,255,.3)}[data-theme=dark] .theme-toggle svg{color:#e0e0e0;filter:drop-shadow(0 2px 4px rgba(13,132,255,.2))}[data-theme=dark] .theme-toggle:hover svg{color:#fff;filter:drop-shadow(0 3px 6px rgba(13,132,255,.4))}.theme-toggle .dark-icon,.theme-toggle .light-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transition:all .4s cubic-bezier(.4,0,.2,1)}.theme-toggle .light-icon{opacity:1;transform:translate(-50%,-50%) scale(1) rotate(0deg)}.theme-toggle .dark-icon{opacity:0;transform:translate(-50%,-50%) scale(.8) rotate(-30deg)}[data-theme=dark] .theme-toggle .light-icon{opacity:0;transform:translate(-50%,-50%) scale(.8) rotate(30deg)}[data-theme=dark] .theme-toggle .dark-icon{opacity:1;transform:translate(-50%,-50%) scale(1) rotate(0deg)}.app-main-icon *,.nav-icon svg,.tab-icon svg,.theme-toggle svg,.theme-toggle svg *{transition:all .3s cubic-bezier(.4,0,.2,1)}.theme-toggle{position:relative;width:40px;height:40px;display:flex;align-items:center;justify-content:center}.theme-toggle:hover{transform:scale(1.05)}.theme-toggle:active{transform:scale(.95)}.theme-toggle.switching::after{content:"";position:absolute;top:50%;left:50%;width:20px;height:20px;background:var(--accent-color);border-radius:50%;transform:translate(-50%,-50%) scale(0);animation:themeSwitch .6s cubic-bezier(.4,0,.2,1);pointer-events:none;z-index:-1}.container{max-width:800px;margin:24px auto;padding:0 16px}.card{background-color:var(--card-background);border-radius:12px;box-shadow:var(--card-shadow);margin-bottom:24px;overflow:visible;outline:0}.card:focus{box-shadow:0 0 0 2px rgba(13,132,255,.3),var(--card-shadow)}.card-title{font-size:18px;font-weight:600;padding:18px 20px;border-bottom:1px solid var(--border-color);display:flex;align-items:center;gap:10px;color:var(--text-color)}.card-content,.status-item{padding:16px;overflow:visible}.status-item{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;padding:12px 16px;background-color:rgba(var(--accent-color-rgb, 13, 132, 255),.02);border-radius:12px;border:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);position:relative;transition:all .3s cubic-bezier(.4,0,.2,1)}.status-item:hover{background-color:rgba(var(--accent-color-rgb, 13, 132, 255),.05);border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.15);transform:translateY(-1px);box-shadow:0 4px 12px rgba(0,0,0,.08)}.status-item:last-child{margin-bottom:0}.status-item>span:first-child{font-weight:500;color:var(--text-color);display:flex;align-items:center;gap:8px}.card-universal{background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);border:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.card-universal::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:radial-gradient(circle at top right,rgba(var(--accent-color-rgb, 13, 132, 255),.03) 0%,transparent 70%);pointer-events:none}.card-universal:hover{transform:translateY(-2px);box-shadow:0 8px 24px rgba(0,0,0,.12);border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.15)}.card-universal .card-title{background:linear-gradient(135deg,transparent 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.05) 100%);border-bottom:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.1);position:relative}.card-universal .card-title::after{content:"";position:absolute;bottom:0;left:20px;right:20px;height:2px;background:linear-gradient(90deg,var(--accent-color) 0%,transparent 100%);opacity:.3}.card-universal .card-content{position:relative;z-index:1;padding:20px}.page.active#page-config{display:flex;flex-direction:column;gap:28px}.page.active#page-config>.card{margin-bottom:0}#settingsCard .status-item{margin-bottom:24px;padding:16px 20px;background-color:transparent;border:0;border-radius:8px}#settingsCard .status-item:hover{background-color:rgba(var(--accent-color-rgb, 13, 132, 255),.03);transform:none;box-shadow:none}.setting-description{margin-top:8px;margin-bottom:16px;color:var(--secondary-text-color);font-size:12px}#settingsCard .setting-description{margin-bottom:24px}.select-container{min-width:150px;position:relative;overflow:visible}.card-title .title-icon{display:flex;align-items:center;justify-content:center;width:32px;height:32px;background:linear-gradient(135deg,#0d84ff 0,#147ce5 100%);border-radius:8px;box-shadow:0 2px 8px rgba(13,132,255,.3);color:#fff;position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1);flex-shrink:0;margin-right:12px}.card-title .title-icon::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,.3)0,transparent 50%);border-radius:8px;opacity:0;transition:opacity .3s ease}.card-title .title-icon svg{width:18px;height:18px;color:#fff;fill:#fff;transition:all .3s ease;z-index:1;position:relative}.card:hover .card-title .title-icon{transform:scale(1.15) rotate(3deg);box-shadow:0 4px 16px rgba(13,132,255,.4)}.card:hover .card-title .title-icon::before,.status-badge:hover::before,.status-item:hover .miuix-status-icon::before,.version-badge:hover::before{opacity:1}.card:hover .card-title .title-icon svg{transform:scale(1.1)}.status-item:hover .miuix-status-icon{transform:scale(1.1) rotate(2deg);box-shadow:0 4px 12px rgba(13,132,255,.4)}.status-item:hover .miuix-status-icon svg.status-icon{transform:scale(1.05)}.status-item>span:first-child .miuix-status-icon{width:24px;height:24px;background:linear-gradient(135deg,#0d84ff 0,#147ce5 100%);border-radius:6px;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 6px rgba(13,132,255,.3);position:relative;overflow:hidden;flex-shrink:0;transition:all .3s cubic-bezier(.4,0,.2,1);min-width:24px;min-height:24px;margin-right:8px}.status-item>span:first-child .miuix-status-icon::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,.2)0,transparent 50%);border-radius:6px;opacity:0;transition:opacity .3s ease}.status-item>span:first-child .miuix-status-icon svg.status-icon{width:14px;height:14px;color:#fff;fill:#fff;opacity:1;z-index:1;position:relative}.status-badge{padding:6px 16px 6px 20px;border-radius:16px;font-size:13px;font-weight:600;display:inline-flex;align-items:center;gap:6px;box-shadow:0 2px 6px rgba(0,0,0,.1);transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden;transform:translateZ(0)}.status-badge::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,.2)0,transparent 50%);border-radius:16px;opacity:0;transition:opacity .3s ease}.status-badge:hover{transform:translateY(-1px) scale(1.02);box-shadow:0 4px 12px rgba(0,0,0,.15)}.status-badge::after{content:"";position:absolute;width:6px;height:6px;border-radius:50%;left:8px;top:50%;transform:translateY(-50%)}.status-running{background:linear-gradient(135deg,var(--success-color) 0%,#66bb6a 100%);color:#fff;box-shadow:0 2px 8px rgba(76,175,80,.3)}.mode-badge.powersave:hover,.status-running:hover{box-shadow:0 4px 16px rgba(76,175,80,.4)}.status-running::after{background-color:rgba(255,255,255,.8)}.status-stopped{background:linear-gradient(135deg,var(--error-color) 0%,#ef5350 100%);color:#fff;box-shadow:0 2px 8px rgba(244,67,54,.3)}.status-stopped:hover{box-shadow:0 4px 16px rgba(244,67,54,.4)}.status-stopped::after{background-color:rgba(255,255,255,.6)}.version-badge{padding:6px 16px 6px 20px;border-radius:16px;font-size:13px;font-weight:600;background:linear-gradient(135deg,var(--accent-color) 0%,var(--accent-hover) 100%);color:#fff;display:inline-flex;align-items:center;gap:6px;box-shadow:0 2px 8px rgba(13,132,255,.3);transform:translateZ(0)}.version-badge::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,.2)0,transparent 50%);border-radius:16px;opacity:0;transition:opacity .3s ease}.version-badge:hover{transform:translateY(-1px) scale(1.02);box-shadow:0 4px 16px rgba(13,132,255,.4)}.mode-badge::after,.version-badge::after{content:"";position:absolute;width:6px;height:6px;border-radius:50%;left:8px;top:50%;transform:translateY(-50%);background-color:rgba(255,255,255,.8)}#appIconCard,.mode-badge,.version-badge{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.mode-badge{padding:6px 16px 6px 20px;border-radius:16px;font-size:13px;font-weight:600;display:inline-flex;align-items:center;gap:6px;background:linear-gradient(135deg,#9e9e9e 0,#757575 100%);color:#fff;box-shadow:0 2px 6px rgba(0,0,0,.1);transform:translateZ(0)}.mode-badge.powersave{background:linear-gradient(135deg,#4caf50 0,#388e3c 100%);color:#fff;box-shadow:0 2px 8px rgba(76,175,80,.3)}.mode-badge.balance{background:linear-gradient(135deg,#2196f3 0,#1976d2 100%);color:#fff;box-shadow:0 2px 8px rgba(33,150,243,.3)}.mode-badge.performance{background:linear-gradient(135deg,#ff9800 0,#f57c00 100%);color:#fff;box-shadow:0 2px 8px rgba(255,152,0,.3)}.mode-badge.fast{background:linear-gradient(135deg,#f44336 0,#d32f2f 100%);color:#fff;box-shadow:0 2px 8px rgba(244,67,54,.3)}.mode-badge.default{background:linear-gradient(135deg,#9e9e9e 0,#757575 100%);color:#fff;box-shadow:0 2px 8px rgba(158,158,158,.3)}.mode-badge:hover{transform:translateY(-1px) scale(1.02)}.mode-badge.powersave:hover{background:linear-gradient(135deg,#66bb6a 0,#4caf50 100%)}.mode-badge.balance:hover{background:linear-gradient(135deg,#42a5f5 0,#2196f3 100%);box-shadow:0 4px 16px rgba(33,150,243,.4)}.mode-badge.performance:hover{background:linear-gradient(135deg,#ffb74d 0,#ff9800 100%);box-shadow:0 4px 16px rgba(255,152,0,.4)}.mode-badge.fast:hover{background:linear-gradient(135deg,#ef5350 0,#f44336 100%);box-shadow:0 4px 16px rgba(244,67,54,.4)}.mode-badge.powersave::after{background-color:rgba(129,199,132,.8)}.mode-badge.balance::after{background-color:rgba(79,195,247,.8)}.mode-badge.performance::after{background-color:rgba(255,183,77,.8)}.mode-badge.fast::after{background-color:rgba(239,154,154,.8)}.mode-badge.default::after{background-color:rgba(189,189,189,.8)}.status-badge.status-changing{animation:statusChange .6s ease-in-out}#appIconCard{margin-bottom:32px;background:linear-gradient(135deg,var(--accent-bg) 0%,transparent 100%);border:1px solid var(--accent-color);border-radius:20px;cursor:pointer;outline:0;-webkit-tap-highlight-color:transparent}#appIconCard:focus{box-shadow:0 0 0 3px rgba(13,132,255,.3),0 12px 32px rgba(13,132,255,.15);border-color:var(--accent-hover)}#appIconCard:focus:hover{box-shadow:0 0 0 3px rgba(13,132,255,.4),0 12px 32px rgba(13,132,255,.2)}#appIconCard:hover{transform:translateY(-2px);box-shadow:0 12px 32px rgba(13,132,255,.15);border-color:var(--accent-hover);animation:none}#appIconCard::before,.icon-background::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;transition:opacity .3s ease}#appIconCard::before{background:linear-gradient(135deg,rgba(13,132,255,.05)0,transparent 50%);pointer-events:none}#appIconCard:hover::before{opacity:1.5}.app-icon-content{padding:24px 20px;position:relative;z-index:1}.miuix-app-icon{display:flex;align-items:center;gap:16px}.icon-background,copyright-content p{background:linear-gradient(135deg,var(--accent-color) 0%,var(--accent-hover) 100%)}.app-main-icon,.icon-background{position:relative;transition:all .3s cubic-bezier(.4,0,.2,1)}.icon-background{width:64px;height:64px;border-radius:16px;display:flex;align-items:center;justify-content:center;box-shadow:0 8px 20px rgba(13,132,255,.3);overflow:hidden;flex-shrink:0}#appIconCard:hover .icon-background{transform:scale(1.05) rotate(2deg);box-shadow:0 12px 28px rgba(13,132,255,.4)}.icon-background::before{background:linear-gradient(135deg,rgba(255,255,255,.2)0,transparent 50%);border-radius:16px}#appIconCard:hover .icon-background::before{opacity:.7}.app-main-icon{color:#fff;width:40px;height:40px;z-index:1;filter:drop-shadow(0 2px 4px rgba(0,0,0,.2))}#appIconCard:hover .app-main-icon{transform:scale(1.1);filter:drop-shadow(0 4px 8px rgba(0,0,0,.3))}.app-info{flex:1;min-width:0}.app-title{font-size:24px;font-weight:600;color:var(--text-color);margin:0 0 4px;line-height:1.2;transition:color .3s ease}#appIconCard:hover .app-title{color:var(--accent-color)}.app-subtitle,copyright-content{color:var(--secondary-text-color)}.app-subtitle{font-size:14px;margin:0;opacity:.8;font-weight:400;transition:opacity .3s ease}#appIconCard:hover .app-subtitle{opacity:1}#copyrightCard{margin-top:24px;background:linear-gradient(135deg,rgba(var(--accent-color-rgb, 13, 132, 255),.05) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);border:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.1)}copyright-content{text-align:center;padding:20px;font-size:13px;line-height:1.5;position:relative;z-index:1}copyright-content p{margin:0;font-weight:500;background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;text-shadow:none}.card,.cgroup-info,.config-card,.games-config-container,.gpu-info,.modal-content,.settings-card,.voltage-control-card{transition:background-color .3s ease,border-color .3s ease,box-shadow .4s cubic-bezier(.4,0,.2,1),transform .35s cubic-bezier(.34,1.56,.64,1),opacity .4s ease}.animate-float-in{animation:cardFloatIn .75s cubic-bezier(.34,1.56,.64,1) forwards}.animate-glow{animation:cardGlow 3.2s ease-in-out infinite}.animate-fade-out{animation:cardFadeOut .45s ease forwards}.card:hover,.cgroup-info:hover,.config-card:hover,.games-config-container:hover,.gpu-info:hover,.settings-card:hover,.voltage-control-card:hover{transform:translateY(-4px) scale(1.012);box-shadow:0 8px 32px rgba(var(--shadow-rgb, 0, 0, 0),.18),0 4px 12px rgba(var(--shadow-rgb, 0, 0, 0),.08)}.card:active,.cgroup-info:active,.config-card:active,.games-config-container:active,.gpu-info:active,.settings-card:active,.voltage-control-card:active{transform:translateY(0) scale(.99);transition:transform .18s cubic-bezier(.4,0,.2,1)}.responsive-width-transition{transition:width .5s cubic-bezier(.4,0,.2,1),flex-basis .5s cubic-bezier(.4,0,.2,1)}[class*=stagger-]{opacity:0;transform:translateY(18px) scale(.97)}.stagger-1{animation:cardFloatIn .7s .05s forwards cubic-bezier(.34,1.56,.64,1)}.stagger-2{animation:cardFloatIn .7s .12s forwards cubic-bezier(.34,1.56,.64,1)}.stagger-3{animation:cardFloatIn .7s .18s forwards cubic-bezier(.34,1.56,.64,1)}.stagger-4{animation:cardFloatIn .7s .24s forwards cubic-bezier(.34,1.56,.64,1)}.stagger-5{animation:cardFloatIn .7s .3s forwards cubic-bezier(.34,1.56,.64,1)}.stagger-6{animation:cardFloatIn .7s .36s forwards cubic-bezier(.34,1.56,.64,1)}.modal-overlay.show{animation:modalOverlayIn .35s ease forwards}.modal-content.show{animation:modalContentIn .55s cubic-bezier(.34,1.56,.64,1) forwards}.cards-grid,.config-grid,.games-grid,.settings-grid,.status-grid,.voltage-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(var(--card-min-width, 320px),1fr));gap:var(--grid-gap, 16px);align-items:stretch}.dense-grid{grid-template-columns:repeat(auto-fill,minmax(260px,1fr));gap:14px}@media (min-width:1800px){.cards-grid,.config-grid,.games-grid,.settings-grid,.status-grid,.voltage-grid{grid-template-columns:repeat(auto-fill,minmax(340px,1fr))}}@media (max-width:1400px){.cards-grid,.config-grid,.games-grid,.settings-grid,.status-grid,.voltage-grid{grid-template-columns:repeat(auto-fill,minmax(300px,1fr))}}@media (max-width:1200px){.cards-grid,.config-grid,.games-grid,.settings-grid,.status-grid,.voltage-grid{grid-template-columns:repeat(auto-fill,minmax(280px,1fr))}}@media (max-width:992px){:root{--card-min-width:260px}.cards-grid,.config-grid,.games-grid,.settings-grid,.status-grid,.voltage-grid{grid-template-columns:repeat(auto-fill,minmax(260px,1fr));gap:14px}}@media (max-width:820px){.cards-grid,.config-grid,.games-grid,.settings-grid,.status-grid,.voltage-grid{grid-template-columns:repeat(auto-fill,minmax(240px,1fr));gap:14px}.content-area{padding:16px 18px 64px}}@media (max-width:640px){:root{--card-min-width:100%;--grid-gap:14px}.cards-grid,.config-grid,.games-grid,.settings-grid,.status-grid,.voltage-grid{grid-template-columns:1fr;gap:var(--grid-gap)}.card,.cgroup-info,.config-card,.games-config-container,.gpu-info,.settings-card,.voltage-control-card{width:100%;margin:0}#appIconCard .miuix-app-icon{flex-direction:row;align-items:center}#appIconCard .app-title{font-size:22px}}@media (max-width:480px){.content-area{padding:14px 16px 72px}.card,.cgroup-info,.config-card,.games-config-container,.gpu-info,.settings-card,.voltage-control-card{padding:16px 16px 18px}#appIconCard .app-title{font-size:20px}.mode-stats-grid,.stats-grid{grid-template-columns:repeat(auto-fill,minmax(120px,1fr));gap:10px}}@media (max-width:370px){.card,.cgroup-info,.config-card,.games-config-container,.gpu-info,.settings-card,.voltage-control-card{padding:14px 14px 16px}.card h2,.config-card h2,.settings-card h2{font-size:18px}}@media (max-height:540px) and (orientation:landscape){.content-area{padding:12px 16px 48px}.card,.cgroup-info,.config-card,.games-config-container,.gpu-info,.settings-card,.voltage-control-card{padding:14px 16px 16px}#appIconCard .app-title{font-size:20px}}.card-semantic{position:relative;overflow:hidden;border-width:1px;border-style:solid;border-radius:18px;--semantic-glow-opacity:0;transition:border-color .35s ease,background-color .35s ease,box-shadow .4s ease}.card-semantic:hover{--semantic-glow-opacity:0.18}.card-semantic::before{content:"";position:absolute;top:-12%;left:-12%;width:140%;height:140%;background:radial-gradient(circle at 30% 30%,var(--semantic-glow-color, transparent),transparent 70%);opacity:var(--semantic-glow-opacity);pointer-events:none;transition:opacity .45s ease;filter:blur(18px)}.card-info{--semantic-color:var(--info-color, #0d84ff);--semantic-bg:rgba(13, 132, 255, 0.07);--semantic-border:rgba(13, 132, 255, 0.35);--semantic-glow-color:rgba(13, 132, 255, 0.6)}.card-info:hover,.card-success:hover{box-shadow:0 6px 20px rgba(13,132,255,.25),0 2px 8px rgba(13,132,255,.18);border-color:var(--semantic-color)}.card-info,.card-success,.card-warning{background:linear-gradient(145deg,var(--semantic-bg),transparent 100%);border-color:var(--semantic-border)}.card-success{--semantic-color:var(--success-color, #2bbf6a);--semantic-bg:rgba(43, 191, 106, 0.07);--semantic-border:rgba(43, 191, 106, 0.35);--semantic-glow-color:rgba(43, 191, 106, 0.6)}.card-success:hover{box-shadow:0 6px 20px rgba(43,191,106,.25),0 2px 8px rgba(43,191,106,.18)}.card-warning{--semantic-color:var(--warning-color, #ffaf00);--semantic-bg:rgba(255, 175, 0, 0.09);--semantic-border:rgba(255, 175, 0, 0.38);--semantic-glow-color:rgba(255, 175, 0, 0.6)}.card-danger:hover,.card-warning:hover{box-shadow:0 6px 20px rgba(255,175,0,.25),0 2px 8px rgba(255,175,0,.18);border-color:var(--semantic-color)}.card-danger{--semantic-color:var(--danger-color, #ff3b30);--semantic-bg:rgba(255, 59, 48, 0.07);--semantic-border:rgba(255, 59, 48, 0.35);--semantic-glow-color:rgba(255, 59, 48, 0.6)}.card-danger:hover{box-shadow:0 6px 20px rgba(255,59,48,.25),0 2px 8px rgba(255,59,48,.18)}.card-danger,.card-muted,.card-neutral{background:linear-gradient(145deg,var(--semantic-bg),transparent 100%);border-color:var(--semantic-border)}.card-neutral{--semantic-color:var(--neutral-color, #8899aa);--semantic-bg:rgba(136, 153, 170, 0.07);--semantic-border:rgba(136, 153, 170, 0.3);--semantic-glow-color:rgba(136, 153, 170, 0.55)}.card-muted:hover,.card-neutral:hover{box-shadow:0 6px 20px rgba(136,153,170,.25),0 2px 8px rgba(136,153,170,.18);border-color:var(--semantic-color)}.card-muted{--semantic-color:var(--muted-color, #94a2b8);--semantic-bg:rgba(148, 162, 184, 0.05);--semantic-border:rgba(148, 162, 184, 0.25);--semantic-glow-color:rgba(148, 162, 184, 0.45);opacity:.85}.card-muted:hover{opacity:1;box-shadow:0 6px 20px rgba(148,162,184,.25),0 2px 8px rgba(148,162,184,.18)}.card-semantic .accent-bar{position:absolute;left:0;top:0;bottom:0;width:4px;background:linear-gradient(var(--semantic-color),transparent);opacity:.8;border-top-left-radius:4px;border-bottom-left-radius:4px}.card-semantic h2,.card-semantic h3,.card-semantic h4{color:var(--semantic-color)}.semantic-badge,.semantic-text{color:var(--semantic-color, var(--accent-color));font-weight:600}.semantic-badge{display:inline-block;padding:4px 10px;border-radius:20px;font-size:12px;letter-spacing:.3px;background:linear-gradient(135deg,var(--semantic-bg, rgba(13, 132, 255, 0.1)),transparent 100%);border:1px solid var(--semantic-border, rgba(13, 132, 255, 0.4));backdrop-filter:blur(6px)}.semantic-badge.glow{box-shadow:0 4px 12px rgba(0,0,0,.08),0 0 0 1px var(--semantic-border, rgba(13, 132, 255, 0.4))}.status-dot,.status-dot.pulse::after{border-radius:50%;background:var(--semantic-color, var(--accent-color))}.status-dot{display:inline-block;width:10px;height:10px;box-shadow:0 0 0 4px rgba(0,0,0,.08),0 0 0 1px var(--semantic-border, rgba(13, 132, 255, 0.4));margin-right:6px;position:relative}.status-dot.pulse::after{content:"";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:100%;height:100%;animation:statusPulse 2.4s ease-out infinite;opacity:.6}.semantic-header{display:flex;align-items:center;gap:10px;padding:4px 0 12px;margin-bottom:4px;border-bottom:1px solid var(--semantic-border, rgba(13, 132, 255, 0.35))}.semantic-header .icon{width:22px;height:22px;fill:var(--semantic-color, var(--accent-color));opacity:.9}.semantic-divider{height:1px;width:100%;background:linear-gradient(to right,transparent,var(--semantic-border, rgba(13, 132, 255, 0.4)),transparent);margin:14px 0;opacity:.7}.btn{background:linear-gradient(135deg,var(--accent-color) 0%,var(--accent-hover) 100%);color:#fff;border:0;border-radius:12px;padding:12px 24px;cursor:pointer;font-size:14px;font-weight:600;display:inline-flex;align-items:center;gap:8px;box-shadow:0 4px 12px rgba(var(--accent-color-rgb, 13, 132, 255),.3);transition:all .3s cubic-bezier(.4,0,.2,1);outline:0;position:relative;overflow:hidden;transform:translateZ(0);min-height:44px}.btn::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,.2)0,transparent 50%);border-radius:12px;opacity:0;transition:opacity .3s ease}.btn:hover{transform:translateY(-2px) scale(1.02);box-shadow:0 6px 20px rgba(var(--accent-color-rgb, 13, 132, 255),.4);background:linear-gradient(135deg,var(--accent-hover) 0%,var(--accent-color) 100%)}.btn:hover::before{opacity:1}.btn:active{transform:translateY(0) scale(.98);box-shadow:0 2px 8px rgba(var(--accent-color-rgb, 13, 132, 255),.4)}.btn:focus{box-shadow:0 0 0 3px rgba(var(--accent-color-rgb, 13, 132, 255),.3),0 4px 12px rgba(var(--accent-color-rgb, 13, 132, 255),.3)}.btn .btn-icon{display:flex;align-items:center;justify-content:center}.btn .btn-icon svg{width:16px;height:16px;fill:currentColor}.btn-primary{background:linear-gradient(135deg,#00c853 0,#4caf50 100%);box-shadow:0 4px 12px rgba(0,200,83,.3)}.btn-primary:hover{background:linear-gradient(135deg,#00e676 0,#66bb6a 100%);box-shadow:0 6px 20px rgba(0,200,83,.4)}.btn-secondary{background:linear-gradient(135deg,var(--border-color) 0%,var(--secondary-text-color) 100%);color:var(--text-color);box-shadow:0 4px 12px rgba(0,0,0,.1)}.btn-secondary:hover{background:linear-gradient(135deg,var(--secondary-text-color) 0%,var(--border-color) 100%);color:var(--card-background);box-shadow:0 6px 20px rgba(0,0,0,.15)}.btn-danger{background:linear-gradient(135deg,#e74c3c 0,#c0392b 100%);color:#fff;margin-left:auto;box-shadow:0 4px 12px rgba(231,76,60,.3)}.btn-danger:hover{background:linear-gradient(135deg,#ec7063 0,#e74c3c 100%);box-shadow:0 6px 20px rgba(231,76,60,.4)}.form-actions{display:flex;justify-content:flex-end;gap:8px;margin-top:16px}.select{background-color:var(--card-background);color:var(--text-color);border:1px solid var(--border-color);border-radius:4px;padding:8px;font-size:14px}.custom-select{position:relative;width:100%;cursor:pointer;z-index:10}#languageContainer{z-index:30}#logLevelContainer{z-index:20}#globalModeContainer{z-index:15}#globalModeOptions{z-index:16}#gameModeContainer{z-index:10}#gameModeOptions{z-index:11}.options-container,.selected-option{border:1px solid var(--border-color);border-radius:4px}.selected-option{background-color:transparent;color:var(--text-color);padding:8px 12px;font-size:14px;display:flex;justify-content:space-between;align-items:center;transition:all .3s}.selected-option:hover{border-color:var(--accent-color)}.custom-select.open .selected-option{background-color:var(--accent-color);color:#fff;border-color:var(--accent-color)}.selected-option::after{content:"▼";font-size:10px;margin-left:8px;transition:transform .3s;color:var(--secondary-text-color)}.custom-select.open .selected-option::after{transform:rotate(180deg);color:#fff}.options-container{position:absolute;top:100%;left:0;right:0;background-color:var(--card-background);margin-top:4px;box-shadow:0 2px 8px rgba(0,0,0,.2);z-index:11;max-height:0;overflow:hidden;transition:max-height .3s ease;opacity:0}#languageOptions{z-index:31}#logLevelOptions{z-index:21}.custom-select.open .options-container{max-height:200px;overflow:visible;opacity:1}.option{padding:8px 12px;font-size:14px;color:var(--text-color);border-bottom:1px solid var(--border-color);opacity:0;transform:translateY(-10px);transition:all .3s cubic-bezier(.4,0,.2,1)}.custom-select.open .option{opacity:1;transform:translateY(0)}.option:last-child,tbody tr:last-child td{border-bottom:none}.option:hover{background-color:var(--border-color)}.option.selected{background-color:var(--accent-color);color:#fff}.form-group{margin-bottom:16px}.form-group label{display:block;margin-bottom:8px;font-weight:500}.form-group input,.form-group select{width:100%;padding:12px 16px;border:2px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);border-radius:12px;background:linear-gradient(135deg,var(--background-color) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);color:var(--text-color);font-size:14px;transition:all .3s cubic-bezier(.4,0,.2,1);box-shadow:0 2px 6px rgba(0,0,0,.05)}.form-group input:focus,.form-group select:focus{outline:0;border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.4);background:linear-gradient(135deg,var(--background-color) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.05) 100%);box-shadow:0 0 0 3px rgba(var(--accent-color-rgb, 13, 132, 255),.2),0 4px 12px rgba(0,0,0,.08);transform:translateY(-1px)}.form-group input:hover,.form-group select:hover{border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.15);box-shadow:0 4px 8px rgba(0,0,0,.08)}[data-theme=dark] .form-group input,[data-theme=dark] .form-group select{background:linear-gradient(135deg,var(--background-color) 0%,rgba(13,132,255,.05) 100%);border-color:rgba(13,132,255,.15)}[data-theme=dark] .form-group input:focus,[data-theme=dark] .form-group select:focus{border-color:rgba(13,132,255,.5);background:linear-gradient(135deg,var(--background-color) 0%,rgba(13,132,255,.08) 100%);box-shadow:0 0 0 3px rgba(13,132,255,.3),0 4px 12px rgba(0,0,0,.2)}[data-theme=dark] .form-group input:hover,[data-theme=dark] .form-group select:hover{border-color:rgba(13,132,255,.25)}.input-hint{display:block;margin-top:4px;font-size:12px;color:var(--secondary-text-color)}.miuix-super-switch{background:var(--card-background);border-radius:14px;border:1px solid var(--border-color);margin-bottom:12px;overflow:hidden;transition:all .2s ease;cursor:pointer;user-select:none;position:relative}.miuix-super-switch:hover{background:color-mix(in srgb,var(--card-background) 92%,var(--accent-color));border-color:color-mix(in srgb,var(--border-color) 70%,var(--accent-color))}.miuix-super-switch:active{transform:scale(.98);background:color-mix(in srgb,var(--card-background) 88%,var(--accent-color))}.miuix-super-switch-content{display:flex;align-items:center;justify-content:space-between;padding:16px 20px;min-height:64px}.miuix-super-switch-text{flex:1;display:flex;flex-direction:column;gap:4px;min-width:0}.miuix-super-switch-title{font-size:16px;font-weight:500;color:var(--text-color);line-height:1.3;word-wrap:break-word}.miuix-super-switch-summary{font-size:13px;color:var(--secondary-text-color);line-height:1.4;opacity:.8;word-wrap:break-word}.miuix-switch{position:relative;width:48px;height:28px;flex-shrink:0;margin-left:16px}.miuix-switch-input{opacity:0;position:absolute;width:100%;height:100%;margin:0;cursor:pointer;z-index:2}.miuix-switch-thumb,.miuix-switch-track{position:absolute;transition:all .3s cubic-bezier(.4,0,.2,1)}.miuix-switch-track{top:0;left:0;right:0;bottom:0;background:color-mix(in srgb,var(--text-color) 20%,transparent);border-radius:14px;overflow:hidden}.miuix-switch-thumb{top:2px;left:2px;width:24px;height:24px;background:#fff;border-radius:12px;box-shadow:0 2px 4px rgba(0,0,0,.2);transform:translateX(0)}[data-theme=dark] .miuix-switch-thumb{background:#f0f0f0}.miuix-switch-input:checked+.miuix-switch-track{background:var(--accent-color)}.miuix-switch-input:checked+.miuix-switch-track .miuix-switch-thumb{transform:translateX(20px);background:#fff}.miuix-switch:hover .miuix-switch-track{box-shadow:0 0 0 8px color-mix(in srgb,var(--accent-color) 12%,transparent)}.miuix-switch-input:checked:hover+.miuix-switch-track{background:color-mix(in srgb,var(--accent-color) 90%,#000)}.miuix-switch-input:active+.miuix-switch-track .miuix-switch-thumb{width:26px}.miuix-switch-input:checked:active+.miuix-switch-track .miuix-switch-thumb{transform:translateX(18px)}.miuix-switch-input:disabled+.miuix-switch-track{opacity:.5;cursor:not-allowed}.miuix-switch-input:disabled+.miuix-switch-track .miuix-switch-thumb{background:color-mix(in srgb,var(--text-color) 40%,transparent)}.miuix-super-switch:has(.miuix-switch-input:disabled){opacity:.6;cursor:not-allowed}.miuix-super-switch:has(.miuix-switch-input:disabled):hover{transform:none;background:var(--card-background);border-color:var(--border-color)}.miuix-switch-input:focus-visible+.miuix-switch-track{outline:2px solid var(--accent-color);outline-offset:2px}.miuix-super-switch:focus-within{outline:2px solid var(--accent-color);outline-offset:2px}@media (max-width:768px){.miuix-super-switch-content{padding:14px 16px;min-height:60px}.miuix-super-switch-title{font-size:15px}.miuix-super-switch-summary{font-size:12px}.miuix-switch{margin-left:12px}}@media (prefers-reduced-motion:no-preference){.miuix-super-switch{transition:all .2s cubic-bezier(.4,0,.2,1)}.miuix-switch-thumb,.miuix-switch-track{transition:all .25s cubic-bezier(.4,0,.2,1)}}.miuix-super-switch::before{content:"";position:absolute;top:50%;left:50%;width:0;height:0;border-radius:50%;background:color-mix(in srgb,var(--accent-color) 20%,transparent);transform:translate(-50%,-50%);transition:width .3s ease,height .3s ease;pointer-events:none;z-index:1}.miuix-super-switch:active::before{width:100%;height:100%}.miuix-super-switch-content{position:relative;z-index:2}@media (prefers-contrast:high){.miuix-super-switch{border-width:2px}.miuix-switch-thumb,.miuix-switch-track{border:1px solid var(--text-color)}}[data-theme=dark] .miuix-super-switch:hover{background:color-mix(in srgb,var(--card-background) 85%,var(--accent-color))}[data-theme=dark] .miuix-super-switch:active{background:color-mix(in srgb,var(--card-background) 75%,var(--accent-color))}@supports not (color:color-mix(in srgb,red,blue)){.miuix-super-switch:hover{background:var(--accent-bg)}.miuix-switch-track{background:rgba(128,128,128,.3)}.miuix-switch-input:checked+.miuix-switch-track{background:var(--accent-color)}}.number-spinner,.spinner-btn{display:flex;align-items:center;overflow:hidden}.number-spinner{border:2px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);border-radius:12px;background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);box-shadow:0 2px 8px rgba(0,0,0,.05);transition:all .3s ease}.number-spinner:hover{border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.15);box-shadow:0 4px 12px rgba(0,0,0,.08)}.spinner-btn{background:linear-gradient(135deg,var(--accent-color) 0%,var(--accent-hover) 100%);color:#fff;border:0;width:44px;height:44px;font-size:18px;font-weight:600;cursor:pointer;justify-content:center;transition:all .3s cubic-bezier(.4,0,.2,1);user-select:none;-webkit-user-select:none;-webkit-tap-highlight-color:transparent;position:relative}.spinner-btn::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,.2)0,transparent 50%);opacity:0;transition:opacity .3s ease}.spinner-btn:hover{background:linear-gradient(135deg,var(--accent-hover) 0%,var(--accent-color) 100%);transform:scale(1.05);box-shadow:0 4px 12px rgba(var(--accent-color-rgb, 13, 132, 255),.3)}.spinner-btn:hover::before{opacity:1}.spinner-btn:active{transform:scale(.95);box-shadow:0 2px 6px rgba(var(--accent-color-rgb, 13, 132, 255),.3)}.spinner-btn:disabled{background:linear-gradient(135deg,var(--border-color) 0%,var(--secondary-text-color) 100%);cursor:not-allowed;opacity:.7;transform:none;box-shadow:none}.spinner-btn:disabled::before{display:none}.spinner-value{flex:1;text-align:center;padding:12px 16px;font-size:16px;font-weight:600;color:var(--text-color);background:rgba(var(--accent-color-rgb, 13, 132, 255),.05);border-left:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.1);border-right:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.1);transition:all .3s ease}[data-theme=dark] .number-spinner{background:linear-gradient(135deg,var(--card-background) 0%,rgba(13,132,255,.05) 100%);border-color:rgba(13,132,255,.15)}[data-theme=dark] .number-spinner:hover{border-color:rgba(13,132,255,.25)}[data-theme=dark] .spinner-value{background:rgba(13,132,255,.08);border-left-color:rgba(13,132,255,.2);border-right-color:rgba(13,132,255,.2)}.mode-config-container{margin-top:20px}.mode-config-section{display:none}.mode-config-section.active{display:block;animation:fadeIn .3s ease-in-out}.mode-tabs-grid{display:grid;grid-template-columns:repeat(4,1fr);gap:10px;margin-bottom:20px}@media (max-width:768px){.mode-tabs-grid{grid-template-columns:repeat(2,1fr)}}.table-container{overflow-x:auto;border-radius:12px;background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);border:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);box-shadow:0 2px 8px rgba(0,0,0,.05)}table{width:100%;border-collapse:collapse;background:0 0}td,th{padding:16px;text-align:left;border-bottom:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);transition:background-color .3s ease}th{font-weight:600;color:var(--text-color);background:linear-gradient(135deg,transparent 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.05) 100%);font-size:14px}tbody tr{transition:all .3s ease}.games-list li:hover,tbody tr:hover{background:rgba(var(--accent-color-rgb, 13, 132, 255),.05)}tbody tr:hover{transform:translateY(-1px)}[data-theme=dark] .table-container{background:linear-gradient(135deg,var(--card-background) 0%,rgba(13,132,255,.05) 100%);border-color:rgba(13,132,255,.15)}[data-theme=dark] td,[data-theme=dark] th{border-bottom-color:rgba(13,132,255,.15)}[data-theme=dark] th{background:linear-gradient(135deg,transparent 0,rgba(13,132,255,.08) 100%)}[data-theme=dark] tbody tr:hover{background:rgba(13,132,255,.08)}.config-actions,.margin-actions{display:flex;justify-content:space-between;margin-top:16px}.delete-btn,.edit-btn,.games-list li{display:flex;align-items:center;background:0 0}.delete-btn,.delete-btn svg,.edit-btn,.edit-btn svg{transition:all .3s cubic-bezier(.4,0,.2,1);position:relative}.delete-btn,.edit-btn{border:2px solid transparent;cursor:pointer;padding:8px;border-radius:10px;justify-content:center;overflow:hidden}.delete-btn::before,.edit-btn::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border-radius:10px;opacity:0;transition:opacity .3s ease}.delete-btn svg,.edit-btn svg{width:18px;height:18px;z-index:1}.edit-btn{color:var(--accent-color)}.edit-btn::before{background:linear-gradient(135deg,rgba(var(--accent-color-rgb, 13, 132, 255),.1) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.05) 100%)}.edit-btn:hover,delete-btn:hover{transform:translateY(-2px) scale(1.05)}.edit-btn:hover{border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.3);box-shadow:0 4px 12px rgba(var(--accent-color-rgb, 13, 132, 255),.2)}.delete-btn:hover::before,.edit-btn:hover::before{opacity:1}.delete-btn{color:var(--error-color)}.delete-btn::before{background:linear-gradient(135deg,rgba(231,76,60,.1)0,rgba(244,67,54,.05) 100%)}delete-btn:hover{border-color:rgba(231,76,60,.3);box-shadow:0 4px 12px rgba(231,76,60,.2)}.delete-btn:hover svg,.edit-btn:hover svg{transform:scale(1.15) rotate(5deg)}.delete-btn:active,.edit-btn:active{transform:translateY(0) scale(.95);box-shadow:0 2px 6px rgba(0,0,0,.1)}[data-theme=dark] .edit-btn::before{background:linear-gradient(135deg,rgba(13,132,255,.15)0,rgba(13,132,255,.08) 100%)}[data-theme=dark] .delete-btn::before{background:linear-gradient(135deg,rgba(231,76,60,.15)0,rgba(244,67,54,.08) 100%)}.games-list{list-style:none;max-height:300px;overflow-y:auto;background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);border:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);border-radius:12px;padding:8px;box-shadow:inset 0 2px 4px rgba(0,0,0,.05)}.games-list li{padding:12px 16px;border-bottom:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);border-radius:8px;margin-bottom:4px;justify-content:space-between;transition:all .3s ease}.game-button-container{display:flex;gap:8px}.games-list li:hover{transform:translateX(4px);border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.15)}.games-list li:last-child{border-bottom:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);margin-bottom:0}.games-actions{display:flex;justify-content:space-between;gap:12px;margin-top:16px}.game-delete-btn,.game-edit-btn{background:linear-gradient(135deg,rgba(13,132,255,.1)0,rgba(13,132,255,.1) 100%);border:1px solid rgba(13,132,255,.2);border-radius:8px;cursor:pointer;color:var(--primary-color);padding:8px;transition:all .3s cubic-bezier(.4,0,.2,1);display:flex;align-items:center;justify-content:center;box-shadow:0 2px 4px rgba(13,132,255,.1)}.game-delete-btn{background:linear-gradient(135deg,rgba(231,76,60,.1)0,rgba(244,67,54,.1) 100%);border:1px solid rgba(231,76,60,.2);color:var(--error-color)}.game-delete-btn svg,.game-edit-btn svg{width:16px;height:16px;transition:transform .3s ease}.game-delete-btn:hover,.game-edit-btn:hover{background:linear-gradient(135deg,rgba(13,132,255,.15)0,rgba(13,132,255,.15) 100%);border-color:rgba(13,132,255,.4);transform:translateY(-1px) scale(1.05);box-shadow:0 4px 8px rgba(13,132,255,.2)}.game-delete-btn:hover{background:linear-gradient(135deg,rgba(231,76,60,.15)0,rgba(244,67,54,.15) 100%);border-color:rgba(231,76,60,.4);box-shadow:0 4px 8px rgba(231,76,60,.2)}.game-delete-btn:hover svg,game-edit-btn:hover svg{transform:scale(1.1)}[data-theme=dark] .games-list{background:linear-gradient(135deg,var(--card-background) 0%,rgba(13,132,255,.05) 100%);border-color:rgba(13,132,255,.15);box-shadow:inset 0 2px 4px rgba(0,0,0,.2)}.form-select{width:100%;padding:10px 12px;border:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.2);border-radius:8px;background-color:var(--card-background);color:var(--text-color);font-size:14px;transition:all .3s ease;box-sizing:border-box}.form-select:focus{outline:0;border-color:var(--primary-color);box-shadow:0 0 0 2px rgba(13,132,255,.2)}[data-theme=dark] .form-select{background-color:rgba(30,30,30,.7);border-color:rgba(13,132,255,.3)}.page-container{position:relative;min-height:calc(100vh - 200px);overflow:visible}.page{display:none;animation:fadeIn .3s ease-in-out;overflow:visible}.page.active{display:block}.nav-bar{display:flex;justify-content:space-around;align-items:flex-start;position:fixed;bottom:0;left:0;width:100%;height:80px;background-color:var(--card-background);box-shadow:0-2px 10px rgba(0,0,0,.1);z-index:90;border-top:1px solid var(--border-color);padding-top:10px;-webkit-tap-highlight-color:transparent;user-select:none}.nav-icon,.nav-item{display:flex;align-items:center}.nav-item{flex-direction:column;justify-content:flex-start;width:25%;height:60px;background:0 0;border:0;color:var(--secondary-text-color);cursor:pointer;transition:all .3s;padding:0;position:relative;overflow:visible;-webkit-tap-highlight-color:transparent;outline:0}.nav-item.active{color:var(--accent-color)}.nav-item.active::before{content:"";position:absolute;top:0;width:70%;height:42px;background-color:var(--accent-bg);border-radius:18px;z-index:-1;transition:all .3s}.nav-icon{font-size:20px;margin-bottom:4px;transition:transform .3s;margin-top:0;justify-content:center;transform:translateY(12px)}.nav-icon svg{width:20px;height:20px;transition:transform .3s}.nav-text{font-size:12px;height:0;opacity:0;overflow:hidden;transition:all .3s;margin-top:4px}.nav-item.active .nav-icon{transform:translateY(8px)}.nav-item.active .nav-icon svg{transform:translateY(0)}.nav-item.active .nav-text{height:16px;opacity:1;transform:translateY(14px)}.settings-tabs-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:8px;width:100%;max-width:400px}.settings-tab-btn{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:14px 10px;background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);border:2px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);border-radius:12px;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);color:var(--text-color);min-height:70px;position:relative;overflow:hidden;font-size:13px;line-height:1.3;text-align:center;box-shadow:0 2px 6px rgba(0,0,0,.05)}.log-tab-btn::before,.settings-tab-btn::before{background:linear-gradient(135deg,rgba(255,255,255,.1)0,transparent 50%);opacity:0;transition:opacity .3s ease}.settings-tab-btn::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border-radius:12px}.settings-tab-btn:hover{border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.3);background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.08) 100%);transform:translateY(-2px) scale(1.02);box-shadow:0 6px 16px rgba(0,0,0,.1)}.log-tab-btn.active::before,.log-tab-btn:hover::before,.refresh-btn:hover::before,.settings-tab-btn.active::before,.settings-tab-btn:hover::before{opacity:1}.settings-tab-btn.active{background:linear-gradient(135deg,var(--accent-color) 0%,var(--accent-hover) 100%);border-color:var(--accent-color);color:#fff;box-shadow:0 4px 16px rgba(var(--accent-color-rgb, 13, 132, 255),.3);transform:translateY(-1px)}.settings-tab-btn .tab-icon{font-size:16px;margin-bottom:4px;transition:transform .3s ease;display:flex;align-items:center;justify-content:center}.log-tab-btn .tab-icon svg,.settings-tab-btn .tab-icon svg{width:16px;height:16px;transition:transform .3s ease}.settings-tab-btn .tab-text{font-size:11px;font-weight:500;text-align:center;line-height:1.2}.settings-tab-btn:hover .tab-icon,.settings-tab-btn:hover .tab-icon svg{transform:scale(1.05)}.settings-tab-btn.active .tab-icon{animation:pulse 2s infinite}.language-tabs-grid{grid-template-columns:repeat(3,1fr);max-width:360px}.log-level-tabs-grid{grid-template-columns:repeat(2,1fr);max-width:300px}.log-header{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:16px;gap:16px}.log-tabs-grid{display:grid;grid-template-columns:1fr 1fr;gap:8px;flex:1;max-width:300px}.log-tab-btn{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:16px 12px;background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);border:2px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);border-radius:16px;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);color:var(--text-color);min-height:80px;position:relative;overflow:hidden;box-shadow:0 2px 8px rgba(0,0,0,.05)}.log-tab-btn::before{border-radius:16px}.log-tab-btn:hover{border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.3);background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.08) 100%);transform:translateY(-3px) scale(1.02);box-shadow:0 8px 20px rgba(0,0,0,.12)}.log-tab-btn.active{background:linear-gradient(135deg,var(--accent-color) 0%,var(--accent-hover) 100%);border-color:var(--accent-color);color:#fff;box-shadow:0 6px 20px rgba(var(--accent-color-rgb, 13, 132, 255),.4);transform:translateY(-2px)}.log-tab-btn .tab-icon{font-size:20px;margin-bottom:4px;transition:transform .3s ease;display:flex;align-items:center;justify-content:center}.refresh-icon svg{width:16px;height:16px;transition:transform .6s cubic-bezier(.4,0,.2,1)}.log-tab-btn .tab-icon svg{width:20px;height:20px}.log-tab-btn .tab-text{font-size:12px;font-weight:500;text-align:center;line-height:1.2}.log-tab-btn:hover .tab-icon,.log-tab-btn:hover .tab-icon svg{transform:scale(1.1)}.log-tab-btn.active .tab-icon,.log-tab-btn.active .tab-icon svg{animation:pulse 2s infinite}.modal h3,.refresh-btn{font-weight:600;position:relative}.refresh-btn{gap:8px;padding:12px 20px;white-space:nowrap;flex-shrink:0;background:linear-gradient(135deg,rgba(var(--accent-color-rgb, 13, 132, 255),.1) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.05) 100%);border:2px solid rgba(var(--accent-color-rgb, 13, 132, 255),.2);border-radius:12px;color:var(--accent-color);transition:all .3s cubic-bezier(.4,0,.2,1);cursor:pointer;box-shadow:0 2px 8px rgba(var(--accent-color-rgb, 13, 132, 255),.1);overflow:hidden}.log-tab-btn::before,.modal-content::before,.refresh-btn::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0}.refresh-btn::before{background:linear-gradient(135deg,rgba(255,255,255,.2)0,transparent 50%);border-radius:12px;opacity:0;transition:opacity .3s ease}.refresh-btn:hover{background:linear-gradient(135deg,rgba(var(--accent-color-rgb, 13, 132, 255),.15) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.08) 100%);border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.4);transform:translateY(-2px) scale(1.02);box-shadow:0 6px 16px rgba(var(--accent-color-rgb, 13, 132, 255),.2)}.refresh-btn:active{transform:translateY(0) scale(.98);box-shadow:0 2px 8px rgba(var(--accent-color-rgb, 13, 132, 255),.2)}.log-actions,.refresh-btn,.refresh-icon{display:flex;align-items:center}.refresh-icon{font-size:16px;transition:transform .6s cubic-bezier(.4,0,.2,1);justify-content:center}.refresh-btn:hover .refresh-icon,.refresh-btn:hover .refresh-icon svg{transform:rotate(360deg) scale(1.1)}[data-theme=dark] .refresh-btn{background:linear-gradient(135deg,rgba(13,132,255,.15)0,rgba(13,132,255,.08) 100%);border-color:rgba(13,132,255,.3)}[data-theme=dark] .refresh-btn:hover{background:linear-gradient(135deg,rgba(13,132,255,.2)0,rgba(13,132,255,.12) 100%);border-color:rgba(13,132,255,.5)}.log-actions{justify-content:space-between;margin-bottom:12px}.log-actions .select-container{min-width:120px}.log-content{background:linear-gradient(135deg,var(--background-color) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);border:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);border-radius:12px;padding:16px;font-family:"Monaco","Menlo","Ubuntu Mono",monospace;font-size:13px;line-height:1.5;white-space:pre-wrap;max-height:500px;overflow-y:auto;transition:all .3s ease-in-out;animation:slideIn .3s ease-out;box-shadow:inset 0 2px 4px rgba(0,0,0,.05);color:var(--text-color)}.log-content:hover{border-color:rgba(var(--accent-color-rgb, 13, 132, 255),.15);box-shadow:inset 0 2px 8px rgba(0,0,0,.08)}[data-theme=dark] .log-content{background:linear-gradient(135deg,var(--background-color) 0%,rgba(13,132,255,.05) 100%);border-color:rgba(13,132,255,.15);box-shadow:inset 0 2px 4px rgba(0,0,0,.2)}[data-theme=dark] .log-content:hover{border-color:rgba(13,132,255,.25);box-shadow:inset 0 2px 8px rgba(0,0,0,.3)}.loading-text{text-align:center;color:var(--secondary-text-color);padding:12px}.modal{display:none;position:fixed;z-index:2000;left:0;top:0;width:100%;height:100%;overflow:auto;background:rgba(0,0,0,.6);backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px);animation:modalFadeIn .3s ease-out}.modal-content{background:linear-gradient(135deg,var(--card-background) 0%,rgba(var(--accent-color-rgb, 13, 132, 255),.02) 100%);margin:10%auto;padding:32px;border-radius:20px;border:1px solid rgba(var(--accent-color-rgb, 13, 132, 255),.08);box-shadow:0 20px 40px rgba(0,0,0,.15);width:90%;max-width:500px;position:relative;z-index:2001;animation:modalSlideIn .3s cubic-bezier(.4,0,.2,1);transform-origin:center top}.modal-content::before{background:radial-gradient(circle at top right,rgba(var(--accent-color-rgb, 13, 132, 255),.03) 0%,transparent 70%);border-radius:20px;pointer-events:none}.close-modal{color:var(--secondary-text-color);position:absolute;top:16px;right:20px;font-size:28px;font-weight:700;cursor:pointer;width:32px;height:32px;display:flex;align-items:center;justify-content:center;border-radius:50%;transition:all .3s ease;background:0 0}.close-modal:hover{color:var(--text-color);background:rgba(var(--accent-color-rgb, 13, 132, 255),.1);transform:scale(1.1)}.modal h3{margin:0 0 24px;color:var(--text-color);font-size:20px;padding-right:40px;z-index:1}[data-theme=dark] .modal{background:rgba(0,0,0,.8)}[data-theme=dark] .modal-content{background:linear-gradient(135deg,var(--card-background) 0%,rgba(13,132,255,.05) 100%);border-color:rgba(13,132,255,.15);box-shadow:0 20px 40px rgba(0,0,0,.4)}[data-theme=dark] .modal-content::before{background:radial-gradient(circle at top right,rgba(13,132,255,.08)0,transparent 70%)}svg{vertical-align:middle}.delete-btn svg,.edit-btn svg,.nav-icon svg,.refresh-icon svg,.tab-icon svg,.theme-toggle svg,svg{color:inherit;fill:currentColor}button:hover svg{transition:transform .2s ease}@media (max-width:480px){.theme-toggle{width:44px;height:44px;padding:8px;min-width:44px;min-height:44px;border-radius:50%;-webkit-tap-highlight-color:transparent}.theme-toggle svg{width:16px;height:16px}.theme-toggle .light-icon svg circle{animation:none}.theme-toggle .light-icon svg path{animation:sunRays 6s linear infinite}.theme-toggle .dark-icon svg path:first-child{animation:moonGlow 6s ease-in-out infinite}}@media (prefers-contrast:high){.theme-toggle{border:2px solid var(--text-color);border-radius:50%;-webkit-tap-highlight-color:transparent}.theme-toggle svg{filter:none}.theme-toggle:hover{background-color:var(--text-color)}.theme-toggle:hover svg{color:var(--background-color)}}.theme-toggle::selection{background:0 0}.theme-toggle::-moz-selection{background:0 0}.theme-toggle,.theme-toggle *{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}@media (prefers-reduced-motion:reduce){.theme-toggle,.theme-toggle .dark-icon,.theme-toggle .light-icon,.theme-toggle svg,.theme-toggle svg *{animation:none;transition:opacity .2s ease,transform .2s ease}.theme-toggle{border-radius:50%;-webkit-tap-highlight-color:transparent}.theme-toggle::after,.theme-toggle::before{display:none}}.theme-toggle{isolation:isolate}[data-theme=light] .theme-toggle{border:1px solid rgba(0,0,0,.08)}[data-theme=dark] .theme-toggle{border:1px solid rgba(255,255,255,.12)}